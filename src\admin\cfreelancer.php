<?php

// Use the Freelancer class with its namespace
use App\classes\Freelancer;
use App\classes\FreelancerExperiencia;

use App\classes\FreelancerDisponibilidad;
use App\classes\FreelancerTarifa;
use App\classes\FreelancerReferencia;
use App\classes\FreelancerDocumento;

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

// --- Include necessary files ---
// Adjust paths based on the actual location of cfreelancer.php relative to the project root
require_once dirname(__DIR__, 2) . '/config/config.php';        // Defines __ROOT__, constants etc.
require_once __ROOT__ . '/src/sessions/sessions.php';           // For session handling & constants
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';        // For validation functions if needed here
require_once __ROOT__ . '/src/general/preparar.php';            // Handles login check, feedback var init, PDO connection ($conexion)
// --- End Include files ---

#region region init variables
/** @var Freelancer|null $freelancer */
$freelancer = null;                                             // Initialize as null for the specific freelancer
// Feedback variables ($success_text, $error_text etc.) are initialized in preparar.php
// This controller primarily SETS flash messages on error/redirect. The VIEW will display them.
#endregion init variables

#region region Handle Request Method & Get Freelancer ID
// This page should only respond to POST requests containing the ID
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
	// If accessed directly via GET or another method, redirect to the list
	$_SESSION['flash_message_error'] = "Acceso inválido a la página de perfil.";
	header('Location: lfreelancers'); // Use the URL defined in .htaccess or your routing
	exit;
}

$freelancer_id = null;
if (isset($_POST['id']) && filter_var($_POST['id'], FILTER_VALIDATE_INT, ['options' => ['min_range' => 1]])) {
	$freelancer_id = (int)$_POST['id'];
} else {
	// Handle error: No valid ID provided via POST
	$_SESSION['flash_message_error'] = "ID de freelancer no proporcionado o inválido para ver el perfil.";
	header('Location: lfreelancers'); // Redirect back to the list
	exit;
}
#endregion Handle Request Method & Get Freelancer ID

#region region Fetch Freelancer Data
try {
	// Use the static method from the Freelancer class to find the freelancer by ID
	// Ensure findById method accepts and uses the PDO connection
	$freelancer = Freelancer::get($freelancer_id, $conexion);
	
	if (!$freelancer) {
		// Handle error: Freelancer not found in the database
		$_SESSION['flash_message_error'] = "Freelancer con ID " . htmlspecialchars($freelancer_id, ENT_QUOTES, 'UTF-8') . " no encontrado.";
		header('Location: lfreelancers'); // Redirect back to the list
		exit;
	}
	
	// Add checks here if the fetched freelancer is active or meets other criteria if necessary
	if ($freelancer->getEstado() == 0) {
		$_SESSION['flash_message_error'] = "El freelancer con ID " . htmlspecialchars($freelancer_id, ENT_QUOTES, 'UTF-8') . " no está activo.";
		header('Location: lfreelancers');
		exit;
	}
	
	//Asogiar el resto de informacion si no hubieron errores con la clase Freelancer
	$freelancer->setExperiencia(FreelancerExperiencia::getByFreelancerId($freelancer_id, $conexion));

	$freelancer->setDisponibilidad(FreelancerDisponibilidad::getByFreelancerId($freelancer_id, $conexion));
	$freelancer->setTarifa(FreelancerTarifa::getByFreelancerId($freelancer_id, $conexion));
	$freelancer->setReferencias(FreelancerReferencia::getByFreelancerId($freelancer_id, $conexion));
	$freelancer->setDocumentos(FreelancerDocumento::getByFreelancerId($freelancer_id, $conexion));
	
} catch (PDOException $e) {
	// Specific handling for database errors during fetch
	error_log("Database error fetching freelancer ID {$freelancer_id}: " . $e->getMessage()); // Log detailed error
	$_SESSION['flash_message_error'] = "Error de base de datos al obtener la información del freelancer.";
	header('Location: lfreelancers'); // Redirect back to the list
	exit;
} catch (Exception $e) {
	// General error handling during fetch
	error_log("Error fetching freelancer ID {$freelancer_id}: " . $e->getMessage()); // Log detailed error
	$_SESSION['flash_message_error'] = "Ocurrió un error inesperado al obtener la información del freelancer.";
	header('Location: lfreelancers'); // Redirect back to the list
	exit;
}
#endregion Fetch Freelancer Data

// If execution reaches here, the $freelancer object is valid and populated.
// Load the view file. The $freelancer object will be available in the view.
// Ensure the path to the view file is correct.
require_once __ROOT__ . '/views/admin/cfreelancer.view.php';

?>