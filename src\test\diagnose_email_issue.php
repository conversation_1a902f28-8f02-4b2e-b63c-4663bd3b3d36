<?php
/**
 * Comprehensive Email Diagnostic Script
 * This will help identify exactly why emails aren't being sent
 */

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/classes/phpmailercorreo.php';
require_once __ROOT__ . '/vendor/autoload.php';

echo "=== Email System Comprehensive Diagnostic ===\n\n";

// Step 1: Check basic PHP configuration
echo "1. PHP Configuration Check:\n";
echo "   PHP Version: " . PHP_VERSION . "\n";
echo "   OS: " . PHP_OS . "\n";
echo "   SAPI: " . php_sapi_name() . "\n";

// Check if exec() is enabled
if (function_exists('exec')) {
    echo "   ✓ exec() function is available\n";
} else {
    echo "   ✗ exec() function is disabled\n";
}

// Check if mail functions are available
if (function_exists('mail')) {
    echo "   ✓ mail() function is available\n";
} else {
    echo "   ✗ mail() function is not available\n";
}

// Step 2: Check error log configuration
echo "\n2. Error Logging Check:\n";
$errorLog = ini_get('error_log');
if ($errorLog) {
    echo "   Error log file: {$errorLog}\n";
    if (file_exists($errorLog) && is_readable($errorLog)) {
        echo "   ✓ Error log is readable\n";
    } else {
        echo "   ⚠ Error log file not found or not readable\n";
    }
} else {
    echo "   ⚠ Error log not configured\n";
}

// Step 3: Check background processing setup
echo "\n3. Background Processing Check:\n";
$backgroundDir = __ROOT__ . '/src/background';
$processorFile = $backgroundDir . '/procesar_correos.php';

if (is_dir($backgroundDir)) {
    echo "   ✓ Background directory exists\n";
} else {
    echo "   ✗ Background directory missing\n";
}

if (file_exists($processorFile)) {
    echo "   ✓ Background processor file exists\n";
} else {
    echo "   ✗ Background processor file missing\n";
}

// Step 4: Test temp directory
echo "\n4. Temp Directory Check:\n";
$tempDir = sys_get_temp_dir();
echo "   Temp directory: {$tempDir}\n";
if (is_writable($tempDir)) {
    echo "   ✓ Temp directory is writable\n";
} else {
    echo "   ✗ Temp directory is not writable\n";
}

// Step 5: Test database connection for management emails
echo "\n5. Database Connection Check:\n";
global $conexion;
if ($conexion instanceof PDO) {
    echo "   ✓ Database connection is available\n";
    
    // Check if Configuracion class exists and works
    try {
        use App\classes\Configuracion;
        $parametros = ['descripcion' => 'Destinatario gerencia formulario aliado comercial'];
        $configuraciones = Configuracion::get_list($parametros, $conexion);
        
        if (!empty($configuraciones)) {
            echo "   ✓ Management email recipients configured: " . count($configuraciones) . " found\n";
            foreach ($configuraciones as $config) {
                $email = $config->getValor();
                echo "     - {$email}\n";
            }
        } else {
            echo "   ⚠ No management email recipients configured\n";
        }
    } catch (Exception $e) {
        echo "   ✗ Error checking management email configuration: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ✗ Database connection not available\n";
}

// Step 6: Test SMTP connectivity
echo "\n6. SMTP Connectivity Test:\n";
$smtpHost = 'smtp.hostinger.com';
$smtpPort = 465;

echo "   Testing connection to {$smtpHost}:{$smtpPort}...\n";
$connection = @fsockopen($smtpHost, $smtpPort, $errno, $errstr, 10);
if ($connection) {
    echo "   ✓ SMTP server is reachable\n";
    fclose($connection);
} else {
    echo "   ✗ Cannot connect to SMTP server: {$errstr} ({$errno})\n";
}

// Step 7: Test background email function
echo "\n7. Background Email Function Test:\n";
if (function_exists('enviarCorreoEnBackground')) {
    echo "   ✓ enviarCorreoEnBackground function is available\n";
    
    // Test the function with dummy data
    try {
        $testParams = [
            'nombre_aliado' => 'Test Diagnostic',
            'correo_aliado' => '<EMAIL>',
            'tipo_alianza' => 'Test',
            'telefono_aliado' => '123456789'
        ];
        
        echo "   Testing function call...\n";
        enviarCorreoEnBackground('aliado_comercial', $testParams);
        echo "   ✓ Function called without errors\n";
        
        // Check for temp files
        sleep(1);
        $tempFiles = glob($tempDir . '/lunex_email_*.json');
        if (!empty($tempFiles)) {
            echo "   ⚠ Found unprocessed temp files:\n";
            foreach ($tempFiles as $file) {
                echo "     - {$file}\n";
                // Read and display content
                $content = file_get_contents($file);
                echo "       Content: {$content}\n";
                unlink($file); // Clean up
            }
        } else {
            echo "   ✓ No unprocessed temp files (background processing worked)\n";
        }
        
    } catch (Exception $e) {
        echo "   ✗ Error testing background function: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ✗ enviarCorreoEnBackground function not available\n";
    echo "   Make sure you've included the form file that defines this function\n";
}

// Step 8: Check recent error logs for email-related issues
echo "\n8. Recent Error Log Analysis:\n";
if ($errorLog && file_exists($errorLog)) {
    $logContent = file_get_contents($errorLog);
    $lines = explode("\n", $logContent);
    $recentLines = array_slice($lines, -50); // Last 50 lines
    
    $emailRelatedLines = array_filter($recentLines, function($line) {
        return stripos($line, 'email') !== false || 
               stripos($line, 'correo') !== false || 
               stripos($line, 'background') !== false ||
               stripos($line, 'aliado') !== false ||
               stripos($line, 'smtp') !== false ||
               stripos($line, 'phpmailer') !== false;
    });
    
    if (!empty($emailRelatedLines)) {
        echo "   Recent email-related log entries:\n";
        foreach ($emailRelatedLines as $line) {
            echo "   {$line}\n";
        }
    } else {
        echo "   No recent email-related log entries found\n";
    }
} else {
    echo "   Cannot analyze error log\n";
}

echo "\n=== Diagnostic Summary ===\n";
echo "If emails are still not working, the most likely issues are:\n";
echo "1. SMTP authentication problems (check credentials)\n";
echo "2. Firewall blocking outbound SMTP connections\n";
echo "3. exec() function disabled on server\n";
echo "4. Background processor not executing properly\n";
echo "5. Management email recipients not configured in database\n";
echo "\nRecommendation: Run the direct email test (test_email_direct.php) to bypass background processing\n";
?>
