<?php
/**
 * Check Services in Database
 * Verify that services exist for form testing
 */

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/vendor/autoload.php';

use App\classes\ServicioCategoria;
use App\classes\Servicio;

echo "=== Services Database Check ===\n\n";

try {
    global $conexion;
    
    if (!$conexion instanceof PDO) {
        throw new Exception("Database connection not available");
    }
    
    echo "1. Checking Service Categories:\n";
    $categorias = ServicioCategoria::get_list($conexion);
    
    if (empty($categorias)) {
        echo "   ✗ No service categories found\n";
    } else {
        echo "   ✓ Found " . count($categorias) . " service categories:\n";
        foreach ($categorias as $categoria) {
            echo "     - ID: {$categoria->getId()}, Name: {$categoria->getDescripcion()}\n";
        }
    }
    
    echo "\n2. Checking Services:\n";
    $servicios = Servicio::get_list($conexion);
    
    if (empty($servicios)) {
        echo "   ✗ No services found\n";
    } else {
        echo "   ✓ Found " . count($servicios) . " services:\n";
        foreach ($servicios as $servicio) {
            echo "     - ID: {$servicio->getId()}, Category: {$servicio->getId_servicio_categoria()}, Name: {$servicio->getDescripcion()}\n";
        }
    }
    
    echo "\n3. Services by Category:\n";
    foreach ($categorias as $categoria) {
        $id_categoria = $categoria->getId();
        $servicios_categoria = Servicio::get_list($conexion, $id_categoria);
        echo "   Category '{$categoria->getDescripcion()}' has " . count($servicios_categoria) . " services\n";
    }
    
    // Suggest valid service IDs for testing
    if (!empty($servicios)) {
        $validIds = array_slice(array_map(function($s) { return $s->getId(); }, $servicios), 0, 3);
        echo "\n4. Valid Service IDs for testing: [" . implode(', ', $validIds) . "]\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error checking services: " . $e->getMessage() . "\n";
}

echo "\n=== Check Complete ===\n";
?>
