<?php
#region region DOCS

/** @var Tarea[] $tareas */
/** @var array $tareas_organizadas */
/** @var array $tareas_agentes_counts */
/** @var int|null $filtro_estado */
/** @var int|null $filtro_proyecto_id */
/** @var string|null $nombre_proyecto_filtro */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */

use App\classes\Tarea;
use App\classes\Proyecto;
use App\classes\ProyectoModulo;
use App\classes\TareaAgente;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Tareas</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Gestión de Tareas" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<!-- jQuery UI CSS for autocomplete -->
	<link rel="stylesheet" href="<?php echo RUTA_ADM_ASSETS ?>plugins/jquery-ui-dist/jquery-ui.min.css">
	<?php #endregion HEAD ?>

	<style>
		/* Custom styling for Ver Agentes button */
		.btn-ver-agentes:disabled {
			opacity: 0.6;
			cursor: not-allowed;
		}

		.btn-ver-agentes:disabled:hover {
			opacity: 0.6;
		}

		/* Toast notification styling */
		.toast-container {
			position: fixed;
			top: 20px;
			right: 20px;
			z-index: 1055;
		}

		.toast {
			min-width: 300px;
		}

		.toast-success {
			background-color: #d4edda;
			border-color: #c3e6cb;
			color: #155724;
		}

		.toast-error {
			background-color: #f8d7da;
			border-color: #f5c6cb;
			color: #721c24;
		}

		/* Hierarchical task styling */
		.tarea-padre {
			background-color: rgba(0, 123, 255, 0.05);
			border-left: 3px solid #007bff;
		}

		.tarea-hija {
			background-color: rgba(108, 117, 125, 0.05);
			border-left: 3px solid #6c757d;
		}

		/*.tarea-hija td:first-child {
			padding-left: 2rem;
		}*/

		.tarea-hija .descripcion-hija {
			padding-left: 1.5rem;
			position: relative;
		}

		.tarea-hija .descripcion-hija::before {
			content: "└─";
			position: absolute;
			left: 0;
			color: #6c757d;
			font-weight: bold;
		}

		.btn-toggle-hijas {
			background: none;
			border: none;
			color: #007bff;
			cursor: pointer;
			padding: 0;
			margin-right: 0.5rem;
			font-size: 0.9rem;
		}

		.btn-toggle-hijas:hover {
			color: #0056b3;
		}

		.btn-toggle-hijas i {
			transition: transform 0.2s ease;
		}

		.btn-toggle-hijas.collapsed i {
			transform: rotate(-90deg);
		}

		.tarea-hijas-container {
			transition: all 0.3s ease;
		}

		.badge-hijo-count {
			background-color: #6c757d;
			color: white;
			font-size: 0.7rem;
			margin-left: 0.5rem;
		}

		/* Fix jQuery UI autocomplete z-index in modals */
		.ui-autocomplete {
			z-index: 1060 !important; /* Bootstrap modal z-index is 1055, so we need higher */
			max-height: 200px;
			overflow-y: auto;
			overflow-x: hidden;
		}

		/* Ensure autocomplete menu appears above modal backdrop */
		.modal .ui-autocomplete {
			z-index: 1060 !important;
		}

		/* Style autocomplete items for better visibility in modal */
		.ui-autocomplete .ui-menu-item {
			font-size: 14px;
		}

		.ui-autocomplete .ui-menu-item-wrapper {
			padding: 8px 12px;
		}

		/* Ensure autocomplete doesn't get cut off by modal boundaries */
		#editarTareaModal .ui-autocomplete {
			max-width: 400px;
		}
	</style>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Tareas</h4>
				<?php if (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro): ?>
					<p class="mb-0 text-muted">Tareas del proyecto: <strong><?php echo htmlspecialchars($nombre_proyecto_filtro); ?></strong></p>
				<?php else: ?>
					<p class="mb-0 text-muted">Selecciona un proyecto para ver sus tareas</p>
				<?php endif; ?>
			</div>
			<div class="ms-auto">
				<a href="lproyectos" class="btn btn-outline-primary me-2"><i class="fa fa-folder fa-fw me-1"></i> Ver Proyectos</a>
				<?php if (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro && isset($filtro_proyecto_id) && $filtro_proyecto_id): ?>
					<a href="itarea?proyecto_id=<?php echo $filtro_proyecto_id; ?>" class="btn btn-success"><i class="fa fa-plus-circle fa-fw me-1"></i> Crear nueva</a>
				<?php endif; ?>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region FILTER BUTTONS ?>
		<div class="mb-3">
			<div class="btn-group">
				<?php if (isset($filtro_proyecto_id) && $filtro_proyecto_id): ?>
					<!-- Project-specific filter buttons (maintain project context) -->
					<a href="ltareas" class="btn btn-sm <?php echo !$filtro_estado ? 'btn-primary' : 'btn-default'; ?>">
						Todas
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_PENDIENTE; ?>"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_PENDIENTE ? 'btn-primary' : 'btn-default'; ?>">
						Pendientes
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_EN_PROGRESO; ?>"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_EN_PROGRESO ? 'btn-primary' : 'btn-default'; ?>">
						En Progreso
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_TERMINADO; ?>"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_TERMINADO ? 'btn-primary' : 'btn-default'; ?>">
						Terminadas
					</a>
					<!-- Button to clear project filter and see all tasks -->
					<a href="ltareas?clear_project=1" class="btn btn-sm btn-outline-secondary" title="Ver todas las tareas (sin filtro de proyecto)">
						<i class="fa fa-times"></i> Quitar Filtro
					</a>
				<?php else: ?>
					<!-- Global filter buttons (no project context) -->
					<a href="ltareas?clear_project=1" class="btn btn-sm <?php echo !$filtro_estado ? 'btn-primary' : 'btn-default'; ?>">
						Todas
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_PENDIENTE; ?>&clear_project=1"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_PENDIENTE ? 'btn-primary' : 'btn-default'; ?>">
						Pendientes
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_EN_PROGRESO; ?>&clear_project=1"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_EN_PROGRESO ? 'btn-primary' : 'btn-default'; ?>">
						En Progreso
					</a>
					<a href="ltareas?estado=<?php echo Tarea::ESTADO_TERMINADO; ?>&clear_project=1"
					   class="btn btn-sm <?php echo $filtro_estado === Tarea::ESTADO_TERMINADO ? 'btn-primary' : 'btn-default'; ?>">
						Terminadas
					</a>
				<?php endif; ?>
			</div>
		</div>
		<?php #endregion FILTER BUTTONS ?>

		<?php #region region SPRINT PANEL ?>
		<?php if (isset($filtro_proyecto_id) && $filtro_proyecto_id): ?>
			<div class="panel panel-inverse mt-3 no-border-radious" id="sprint-panel">
				<?php if (isset($active_sprint) && $active_sprint): ?>
					<!-- Active Sprint Display -->
					<div class="panel-heading no-border-radious">
						<h4 class="panel-title">
							<i class="fa fa-rocket me-2"></i>Sprint Activo: <?php echo htmlspecialchars($active_sprint->getDescripcion()); ?>
							<div class="float-end">
								<button type="button" class="btn btn-xs btn-primary me-1" id="btn-edit-sprint-descripcion"
								        data-sprint-id="<?php echo $active_sprint->getId(); ?>" title="Editar descripción">
									<i class="fa fa-edit"></i> Descripción
								</button>
								<button type="button" class="btn btn-xs btn-info me-1" id="btn-edit-sprint-cambios-bd"
								        data-sprint-id="<?php echo $active_sprint->getId(); ?>" title="Editar cambios BD">
									<i class="fa fa-database"></i> BD
								</button>
								<button type="button" class="btn btn-xs btn-warning me-1" id="btn-edit-sprint-cambios-resources"
								        data-sprint-id="<?php echo $active_sprint->getId(); ?>" title="Editar cambios Resources">
									<i class="fa fa-folder"></i> Resources
								</button>
								<button type="button" class="btn btn-xs btn-danger" id="btn-finalize-sprint"
								        data-sprint-id="<?php echo $active_sprint->getId(); ?>" title="Finalizar sprint">
									<i class="fa fa-flag-checkered"></i> Finalizar
								</button>
							</div>
						</h4>
					</div>
					<!-- Sprint Tasks Table -->
					<div class="table-nowrap" style="overflow: auto">
						<table class="table table-hover table-sm">
							<thead>
							<tr>
								<th class="text-center" style="width: 190px;">Acciones</th>
								<th class="text-center" style="width: 30px;">#</th>
								<th>Módulo</th>
								<th>Descripción</th>
								<th class="text-center">Estado</th>
								<th class="text-center">Fecha Terminación</th>
							</tr>
							</thead>
							<tbody class="fs-12px" id="sprint-table-body">
							<?php if (!empty($sprint_tasks_organized)): ?>
								<?php foreach ($sprint_tasks_organized as $item): ?>
									<?php
									$tarea_padre = $item['tarea'];
									$tareas_hijas = $item['hijas'];
									$tiene_hijas = !empty($tareas_hijas);
									?>

									<!-- Sprint Tarea Padre -->
									<tr data-tarea-id="<?php echo $tarea_padre->getId(); ?>" class="tarea-padre">
										<td class="text-center">
											<?php
											$tarea = $tarea_padre; // Set for the include
											include __ROOT__ . '/views/admin/ltareas_acciones.php';
											?>
										</td>
										<td class="text-center"><?php echo htmlspecialchars((string)$tarea_padre->getId()); ?></td>
										<td><?php echo $tarea_padre->getNombreProyectoModulo() ? htmlspecialchars($tarea_padre->getNombreProyectoModulo()) : 'N/A'; ?></td>
										<td>
											<?php if ($tiene_hijas): ?>
												<button type="button" class="btn-toggle-hijas" data-parent-id="<?php echo $tarea_padre->getId(); ?>">
													<i class="fa fa-chevron-down"></i>
												</button>
											<?php endif; ?>
											<?php echo htmlspecialchars($tarea_padre->getDescripcion() ?? 'N/A'); ?>
											<?php if ($tiene_hijas): ?>
												<span class="badge badge-hijo-count"><?php echo count($tareas_hijas); ?> subtarea<?php echo count($tareas_hijas) > 1 ? 's' : ''; ?></span>
											<?php endif; ?>
										</td>
										<td class="text-center"><span class="badge <?php echo $tarea_padre->getBgColor(); ?>"><?php echo htmlspecialchars($tarea_padre->getNombreTareaEstado()); ?></span></td>
										<td class="text-center"><?php echo format_dateyyymmdd($tarea_padre->getFechaTerminacion()); ?></td>
									</tr>

									<!-- Sprint Tareas Hijas -->
									<?php if ($tiene_hijas): ?>
										<?php foreach ($tareas_hijas as $tarea_hija): ?>
											<tr data-tarea-id="<?php echo $tarea_hija->getId(); ?>" class="tarea-hija tarea-hijas-container" data-parent-id="<?php echo $tarea_padre->getId(); ?>">
												<td class="text-center">
													<?php
													$tarea = $tarea_hija; // Set for the include
													include __ROOT__ . '/views/admin/ltareas_acciones.php';
													?>
												</td>
												<td class="text-center"><?php echo htmlspecialchars((string)$tarea_hija->getId()); ?></td>
												<td><?php echo $tarea_hija->getNombreProyectoModulo() ? htmlspecialchars($tarea_hija->getNombreProyectoModulo()) : 'N/A'; ?></td>
												<td>
													<div class="descripcion-hija">
														<?php echo htmlspecialchars($tarea_hija->getDescripcion() ?? 'N/A'); ?>
													</div>
												</td>
												<td class="text-center"><span class="badge <?php echo $tarea_hija->getBgColor(); ?>"><?php echo htmlspecialchars($tarea_hija->getNombreTareaEstado()); ?></span></td>
												<td class="text-center"><?php echo format_dateyyymmdd($tarea_hija->getFechaTerminacion()); ?></td>
											</tr>
										<?php endforeach; ?>
									<?php endif; ?>
								<?php endforeach; ?>
							<?php else: ?>
								<tr>
									<td colspan="6" class="text-center py-4">
										<i class="fa fa-rocket fa-2x text-muted mb-2"></i>
										<p class="text-muted mb-0">No hay tareas asociadas al sprint activo</p>
									</td>
								</tr>
							<?php endif; ?>
							</tbody>
						</table>
					</div>
				<?php else: ?>
					<!-- No Active Sprint - Show Creation Interface -->
					<div class="panel-heading no-border-radious">
						<h4 class="panel-title">
							<i class="fa fa-rocket me-2"></i>Sprint
						</h4>
					</div>
					<div class="panel-body text-center py-5">
						<i class="fa fa-plus-circle fa-3x text-muted mb-3"></i>
						<h5 class="text-muted mb-3">No hay sprint activo</h5>
						<p class="text-muted mb-4">Crea un nuevo sprint para organizar las tareas del proyecto</p>
						<button type="button" class="btn btn-success" id="btn-create-sprint">
							<i class="fa fa-plus me-1"></i> Crear Sprint
						</button>
					</div>
				<?php endif; ?>
			</div>
		<?php endif; ?>
		<?php #endregion SPRINT PANEL ?>

		<?php #region region PANEL TAREAS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Tareas
					<?php if (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro): ?>
						- <?php echo htmlspecialchars($nombre_proyecto_filtro); ?>
					<?php endif; ?>
					<?php if ($filtro_estado === Tarea::ESTADO_PENDIENTE): ?>
						<?php echo (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro) ? ' (Pendientes)' : 'Pendientes'; ?>
					<?php elseif ($filtro_estado === Tarea::ESTADO_EN_PROGRESO): ?>
						<?php echo (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro) ? ' (En Progreso)' : 'En Progreso'; ?>
					<?php elseif ($filtro_estado === Tarea::ESTADO_TERMINADO): ?>
						<?php echo (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro) ? ' (Terminadas)' : 'Terminadas'; ?>
					<?php endif; ?>
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="table-nowrap" style="overflow: auto">
				<?php #region region TABLE TAREAS ?>
				<table class="table table-hover table-sm">
					<thead>
					<tr>
						<th class="text-center" style="width: 190px;">Acciones</th>
						<th class="text-center" style="width: 30px;">#</th>
						<th>Módulo</th>
						<th>Descripción</th>
						<th class="text-center">Estado</th>
						<th class="text-center">Fecha Terminación</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="tarea-table-body">
					<?php if (!empty($tareas_organizadas)): ?>
						<?php foreach ($tareas_organizadas as $item): ?>
							<?php
							$tarea_padre = $item['tarea'];
							$tareas_hijas = $item['hijas'];
							$tiene_hijas = !empty($tareas_hijas);
							?>

							<!-- Tarea Padre -->
							<tr data-tarea-id="<?php echo $tarea_padre->getId(); ?>" class="tarea-padre">
								<td class="text-center">
									<?php
									$tarea = $tarea_padre; // Set for the include
									include __ROOT__ . '/views/admin/ltareas_acciones.php';
									?>
								</td>
								<td class="text-center"><?php echo htmlspecialchars((string)$tarea_padre->getId()); ?></td>
								<td><?php echo $tarea_padre->getNombreProyectoModulo() ? htmlspecialchars($tarea_padre->getNombreProyectoModulo()) : 'N/A'; ?></td>
								<td>
									<?php if ($tiene_hijas): ?>
										<button type="button" class="btn-toggle-hijas" data-parent-id="<?php echo $tarea_padre->getId(); ?>">
											<i class="fa fa-chevron-down"></i>
										</button>
									<?php endif; ?>
									<?php echo htmlspecialchars($tarea_padre->getDescripcion() ?? 'N/A'); ?>
									<?php if ($tiene_hijas): ?>
										<span class="badge badge-hijo-count"><?php echo count($tareas_hijas); ?> subtarea<?php echo count($tareas_hijas) > 1 ? 's' : ''; ?></span>
									<?php endif; ?>
								</td>
								<td class="text-center"><span class="badge <?php echo $tarea_padre->getBgColor(); ?>"><?php echo htmlspecialchars($tarea_padre->getNombreTareaEstado()); ?></span></td>
								<td class="text-center"><?php echo format_dateyyymmdd($tarea_padre->getFechaTerminacion()); ?></td>
							</tr>

							<!-- Tareas Hijas -->
							<?php if ($tiene_hijas): ?>
								<?php foreach ($tareas_hijas as $tarea_hija): ?>
									<tr data-tarea-id="<?php echo $tarea_hija->getId(); ?>" class="tarea-hija tarea-hijas-container" data-parent-id="<?php echo $tarea_padre->getId(); ?>">
										<td class="text-center">
											<?php
											$tarea = $tarea_hija; // Set for the include
											include __ROOT__ . '/views/admin/ltareas_acciones.php';
											?>
										</td>
										<td class="text-center"><?php echo htmlspecialchars((string)$tarea_hija->getId()); ?></td>
										<td><?php echo $tarea_hija->getNombreProyectoModulo() ? htmlspecialchars($tarea_hija->getNombreProyectoModulo()) : 'N/A'; ?></td>
										<td>
											<div class="descripcion-hija">
												<?php echo htmlspecialchars($tarea_hija->getDescripcion() ?? 'N/A'); ?>
											</div>
										</td>
										<td class="text-center"><span class="badge <?php echo $tarea_hija->getBgColor(); ?>"><?php echo htmlspecialchars($tarea_hija->getNombreTareaEstado()); ?></span></td>
										<td class="text-center"><?php echo format_dateyyymmdd($tarea_hija->getFechaTerminacion()); ?></td>
									</tr>
								<?php endforeach; ?>
							<?php endif; ?>
						<?php endforeach; ?>
					<?php else: ?>
						<tr>
							<td colspan="6" class="text-center py-5">
								<?php if (isset($nombre_proyecto_filtro) && $nombre_proyecto_filtro): ?>
									<i class="fa fa-tasks fa-3x text-muted mb-3"></i>
									<p class="text-muted mb-0">No hay tareas para el proyecto "<?php echo htmlspecialchars($nombre_proyecto_filtro); ?>".</p>
								<?php else: ?>
									<i class="fa fa-folder-open fa-3x text-muted mb-3"></i>
									<p class="text-muted mb-2"><strong>Selecciona un proyecto para ver sus tareas</strong></p>
									<p class="text-muted mb-0">Ve a la <a href="lproyectos" class="text-decoration-none">lista de proyectos</a> y haz clic en el botón "Ver Tareas" del proyecto que deseas gestionar.</p>
								<?php endif; ?>
							</td>
						</tr>
					<?php endif; ?>
					</tbody>
				</table>
				<?php #endregion TABLE TAREAS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL TAREAS ?>
	</div>
	<!-- END #content -->

	<!-- Toast notification container -->
	<div class="toast-container" id="toast-container"></div>

	<?php #region region Gestionar Agentes Modal ?>
	<div class="modal fade" id="gestionarAgentesModal" tabindex="-1" aria-labelledby="gestionarAgentesModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-xl">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="gestionarAgentesModalLabel">Gestionar Agentes de la Tarea</h5>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<div id="agentes-loading" class="text-center" style="display: none;">
						<div class="spinner-border text-primary" role="status">
							<span class="visually-hidden">Cargando...</span>
						</div>
						<p class="mt-2">Cargando agentes asociados...</p>
					</div>
					<div id="agentes-error" class="alert alert-danger" style="display: none;"></div>
					<div id="agentes-content">
						<div class="mb-3">
							<strong>Tarea:</strong> <span id="modal-tarea-descripcion"></span>
						</div>

						<!-- Add New Agent Form -->
						<div class="panel panel-inverse mb-3">
							<div class="panel-heading">
								<h4 class="panel-title">Asignar Nuevo Agente</h4>
							</div>
							<div class="panel-body">
								<form id="add-agente-form">
									<div class="row">
										<div class="col-md-4">
											<div class="mb-3">
												<label for="id_agente" class="form-label">Agente <span class="text-danger">*</span></label>
												<select class="form-select" id="id_agente" name="id_agente" required>
													<option value="">-- Seleccione un agente --</option>
													<!-- Options will be populated via AJAX -->
												</select>
											</div>
										</div>
										<div class="col-md-3">
											<div class="mb-3">
												<label for="costo_usd" class="form-label">Costo USD</label>
												<input type="number" class="form-control" id="costo_usd" name="costo_usd"
												       step="0.01" min="0" placeholder="0.00">
											</div>
										</div>
										<div class="col-md-3">
											<div class="mb-3">
												<label for="n_mensajes" class="form-label">N° Mensajes</label>
												<input type="number" class="form-control" id="n_mensajes" name="n_mensajes"
												       min="0" placeholder="0">
											</div>
										</div>
										<div class="col-md-2">
											<div class="mb-3">
												<label class="form-label">&nbsp;</label>
												<button type="submit" class="btn btn-success d-block w-100">
													<i class="fa fa-plus"></i> Asignar
												</button>
											</div>
										</div>
									</div>
								</form>
								<div id="add-agente-feedback"></div>
							</div>
						</div>

						<!-- Existing Agents Table -->
						<div class="panel panel-inverse">
							<div class="panel-heading">
								<h4 class="panel-title">Agentes Asignados</h4>
							</div>
							<div class="panel-body p-0">
								<div class="table-responsive">
									<table class="table table-striped table-hover mb-0">
										<thead>
											<tr class="text-nowrap">
												<th class="text-center w-80px">Acciones</th>
												<th>Agente</th>
												<th class="text-end">Costo USD</th>
												<th class="text-center">N° Mensajes</th>
											</tr>
										</thead>
										<tbody id="agentes-table-body">
											<!-- Content will be populated via AJAX -->
										</tbody>
									</table>
								</div>
								<div id="agentes-empty" class="text-center text-muted p-4" style="display: none;">
									<i class="fa fa-info-circle fa-2x mb-2"></i>
									<p>La tarea no tiene agentes asociados</p>
								</div>
							</div>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
				</div>
			</div>
		</div>
	</div>
	<?php #endregion Gestionar Agentes Modal ?>

	<!-- Edit TareaAgente Modal -->
	<div class="modal fade" id="editAgenteModal" tabindex="-1" aria-labelledby="editAgenteModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form id="edit-agente-form">
					<div class="modal-header">
						<h5 class="modal-title" id="editAgenteModalLabel">Editar Asignación de Agente</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<input type="hidden" id="edit-tarea-agente-id" name="id">
						<div class="mb-3">
							<label for="edit_id_agente" class="form-label">Agente <span class="text-danger">*</span></label>
							<select class="form-select" id="edit_id_agente" name="id_agente" required>
								<option value="">-- Seleccione un agente --</option>
								<!-- Options will be populated via AJAX -->
							</select>
						</div>
						<div class="mb-3">
							<label for="edit_costo_usd" class="form-label">Costo USD</label>
							<input type="number" class="form-control" id="edit_costo_usd" name="costo_usd"
							       step="0.01" min="0" placeholder="0.00">
						</div>
						<div class="mb-3">
							<label for="edit_n_mensajes" class="form-label">N° Mensajes</label>
							<input type="number" class="form-control" id="edit_n_mensajes" name="n_mensajes"
							       min="0" placeholder="0">
						</div>
						<div id="edit-agente-error" class="alert alert-danger" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Actualizar</button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<!-- Edit Tarea Modal -->
	<div class="modal fade" id="editarTareaModal" tabindex="-1" aria-labelledby="editarTareaModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form id="editar-tarea-form">
					<div class="modal-header">
						<h5 class="modal-title" id="editarTareaModalLabel">Editar Tarea</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<input type="hidden" id="editar-tarea-id" name="id">
						<input type="hidden" id="editar-tarea-id-proyecto" name="id_proyecto">
						<input type="hidden" id="editar-modulo-hidden" name="id_proyecto_modulo">

						<div class="mb-3">
							<label for="editar-tarea-descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
							<textarea class="form-control" id="editar-tarea-descripcion" name="descripcion" rows="3" required></textarea>
							<div class="invalid-feedback" id="editar-descripcion-error" style="display: none;">La descripción es requerida.</div>
						</div>

						<div class="mb-3">
							<label for="editar-modulo-autocomplete" class="form-label">Módulo</label>
							<div class="input-group">
								<div class="position-relative flex-grow-1">
									<input type="text"
										   class="form-control"
										   id="editar-modulo-autocomplete"
										   placeholder="Escriba para buscar un módulo..."
										   autocomplete="off">
									<div id="editar-modulo-loading" class="position-absolute top-50 end-0 translate-middle-y me-2" style="display: none;">
										<div class="spinner-border spinner-border-sm text-primary" role="status">
											<span class="visually-hidden">Cargando...</span>
										</div>
									</div>
								</div>
								<button type="button" class="btn btn-outline-success" id="editar-btn-nuevo-modulo" title="Crear nuevo módulo">
									<i class="fa fa-plus"></i>
								</button>
							</div>
							<div id="editar-modulo-error" class="text-danger small" style="display: none;"></div>
						</div>

						<div class="mb-3" id="editar-sprint-association-container" style="display: none;">
							<div class="form-check">
								<input class="form-check-input" type="checkbox" id="editar-sprint-association" name="sprint_association">
								<label class="form-check-label" for="editar-sprint-association">
									<i class="fa fa-rocket me-1"></i>
									<span id="editar-sprint-association-text">Asociar tarea al sprint actual</span>
								</label>
							</div>
							<div class="form-text" id="editar-sprint-association-help">
								<span id="editar-sprint-current-name"></span>
							</div>
						</div>

						<div id="editar-tarea-error" class="alert alert-danger" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Actualizar</button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<!-- Create Modulo Modal for Edit Task -->
	<div class="modal fade" id="editarCreateModuloModal" tabindex="-1" aria-labelledby="editarCreateModuloModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form id="editar-create-modulo-form">
					<div class="modal-header">
						<h5 class="modal-title" id="editarCreateModuloModalLabel">Crear Nuevo Módulo</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="mb-3">
							<label for="editar-nuevo-modulo-descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="editar-nuevo-modulo-descripcion" name="descripcion" required>
						</div>
						<div id="editar-nuevo-modulo-error" class="alert alert-danger" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Crear Módulo</button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<!-- Sprint Creation Modal -->
	<div class="modal fade" id="createSprintModal" tabindex="-1" aria-labelledby="createSprintModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<form id="create-sprint-form">
					<div class="modal-header">
						<h5 class="modal-title" id="createSprintModalLabel">Crear Nuevo Sprint</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="mb-3">
							<label for="sprint-descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="sprint-descripcion" name="descripcion" required>
							<div class="form-text">Se generará automáticamente como: ID{Sprint_ID} {Tu_Descripción}</div>
						</div>
						<div id="create-sprint-error" class="alert alert-danger" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-success">Crear Sprint</button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<!-- Sprint Field Edit Modal -->
	<div class="modal fade" id="editSprintFieldModal" tabindex="-1" aria-labelledby="editSprintFieldModalLabel" aria-hidden="true">
		<div class="modal-dialog modal-lg">
			<div class="modal-content">
				<form id="edit-sprint-field-form">
					<div class="modal-header">
						<h5 class="modal-title" id="editSprintFieldModalLabel">Editar Campo del Sprint</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<input type="hidden" id="edit-sprint-id" name="sprint_id">
						<input type="hidden" id="edit-sprint-field" name="field">

						<div class="mb-3" id="descripcion-field" style="display: none;">
							<label for="edit-sprint-descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
							<input type="text" class="form-control" id="edit-sprint-descripcion" name="descripcion">
						</div>

						<div class="mb-3" id="cambios-bd-field" style="display: none;">
							<label for="edit-sprint-cambios-bd" class="form-label">Cambios en Base de Datos</label>
							<textarea class="form-control" id="edit-sprint-cambios-bd" name="cambios_bd" rows="30"
							          placeholder="Describe los cambios realizados en la base de datos..."></textarea>
						</div>

						<div class="mb-3" id="cambios-resources-field" style="display: none;">
							<label for="edit-sprint-cambios-resources" class="form-label">Cambios en Resources</label>
							<textarea class="form-control" id="edit-sprint-cambios-resources" name="cambios_resources" rows="30"
							          placeholder="Describe los cambios realizados en archivos de recursos..."></textarea>
						</div>

						<div id="edit-sprint-field-error" class="alert alert-danger" style="display: none;"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
						<button type="submit" class="btn btn-primary">Actualizar</button>
					</div>
				</form>
			</div>
		</div>
	</div>

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>
<!-- jQuery UI for autocomplete functionality -->
<script src="<?php echo RUTA_ADM_ASSETS ?>plugins/jquery-ui-dist/jquery-ui.min.js"></script>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
document.addEventListener('DOMContentLoaded', function () {
	// Use event delegation on the table body
	const tableBody = document.getElementById('tarea-table-body');

	if (tableBody) {
		tableBody.addEventListener('click', function (event) {
			const marcarTerminadaButton = event.target.closest('.btn-marcar-terminada');
			const marcarEnProgresoButton = event.target.closest('.btn-marcar-en-progreso');
			const eliminarButton = event.target.closest('.btn-eliminar-tarea');
			const verAgentesButton = event.target.closest('.btn-ver-agentes');
			const editarTareaButton = event.target.closest('.btn-editar-tarea');
			const toggleButton = event.target.closest('.btn-toggle-hijas');

			// --- Handle Mark as Complete Click ---
			if (marcarTerminadaButton) {
				event.preventDefault();
				const tareaId = marcarTerminadaButton.dataset.tareaid;

				swal({
					title: "Confirmar Acción",
					text: "¿Seguro que quieres marcar esta tarea como terminada?",
					icon: "warning",
					buttons: {
						cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
						confirm: {text: "Marcar como Terminada", value: true, visible: true, className: "btn-success", closeModal: true}
					},
				}).then((willComplete) => {
					if (willComplete) {
						performAjaxAction('marcar_terminada', tareaId);
					}
				});
			}

			// --- Handle Mark as In Progress Click ---
			if (marcarEnProgresoButton) {
				event.preventDefault();
				const tareaId = marcarEnProgresoButton.dataset.tareaid;

				swal({
					title: "Confirmar Acción",
					text: "¿Seguro que quieres marcar esta tarea como en progreso?",
					icon: "warning",
					buttons: {
						cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
						confirm: {text: "Marcar como En Progreso", value: true, visible: true, className: "btn-info", closeModal: true}
					},
				}).then((willProgress) => {
					if (willProgress) {
						performAjaxAction('marcar_en_progreso', tareaId);
					}
				});
			}

			// --- Handle Delete Click ---
			if (eliminarButton) {
				event.preventDefault();
				const tareaId = eliminarButton.dataset.tareaid;

				swal({
					title: "Confirmar Eliminación",
					text: "¿Seguro que quieres eliminar esta tarea? Esta acción no se puede deshacer.",
					icon: "warning",
					buttons: {
						cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
						confirm: {text: "Eliminar", value: true, visible: true, className: "btn-danger", closeModal: true}
					},
					dangerMode: true,
				}).then((willDelete) => {
					if (willDelete) {
						performAjaxAction('eliminar', tareaId);
					}
				});
			}

			// --- Handle Toggle Child Tasks Click ---
			if (toggleButton) {
				event.preventDefault();
				const parentId = toggleButton.dataset.parentId;
				const childRows = document.querySelectorAll(`tr.tarea-hija[data-parent-id="${parentId}"]`);
				const icon = toggleButton.querySelector('i');

				// Toggle visibility of child rows
				let isCollapsed = toggleButton.classList.contains('collapsed');

				childRows.forEach(row => {
					if (isCollapsed) {
						// Show child rows
						row.style.display = '';
					} else {
						// Hide child rows
						row.style.display = 'none';
					}
				});

				// Toggle button state
				if (isCollapsed) {
					toggleButton.classList.remove('collapsed');
					icon.className = 'fa fa-chevron-down';
				} else {
					toggleButton.classList.add('collapsed');
					icon.className = 'fa fa-chevron-right';
				}
			}

			// --- Handle Ver Agentes Click ---
			if (verAgentesButton) {
				event.preventDefault();

				const tareaId = verAgentesButton.dataset.tareaid;
				const tareaDescripcion = verAgentesButton.dataset.tareadescripcion;

				// Store current task ID for modal operations
				window.currentTareaId = tareaId;

				// Set the task description in the modal
				document.getElementById('modal-tarea-descripcion').textContent = tareaDescripcion;

				// Show loading state
				document.getElementById('agentes-loading').style.display = 'block';
				document.getElementById('agentes-content').style.display = 'none';
				document.getElementById('agentes-error').style.display = 'none';

				// Load agentes dropdown and existing agentes data
				Promise.all([
					loadAgentesDropdown(),
					loadTareaAgentes(tareaId)
				]).then(() => {
					// Hide loading state
					document.getElementById('agentes-loading').style.display = 'none';
					document.getElementById('agentes-content').style.display = 'block';
				}).catch(error => {
					console.error('Error loading modal data:', error);
					// Hide loading state
					document.getElementById('agentes-loading').style.display = 'none';
					document.getElementById('agentes-content').style.display = 'block';
					// Show error message
					document.getElementById('agentes-error').textContent = 'Error al cargar los datos: ' + error.message;
					document.getElementById('agentes-error').style.display = 'block';
				});
			}

			// --- Handle Edit Task Click ---
			if (editarTareaButton) {
				event.preventDefault();

				const tareaId = editarTareaButton.dataset.tareaid;
				const tareaDescripcion = editarTareaButton.dataset.tareadescripcion;
				const tareaIdProyecto = editarTareaButton.dataset.tareaidproyecto;
				const tareaIdProyectoModulo = editarTareaButton.dataset.tareaidproyectomodulo;
				const tareaNombreProyectoModulo = editarTareaButton.dataset.tareanombreproyectomodulo;

				// Populate the modal form
				document.getElementById('editar-tarea-id').value = tareaId;
				document.getElementById('editar-tarea-descripcion').value = tareaDescripcion;
				document.getElementById('editar-tarea-id-proyecto').value = tareaIdProyecto;
				document.getElementById('editar-modulo-hidden').value = tareaIdProyectoModulo || '';
				document.getElementById('editar-modulo-autocomplete').value = tareaNombreProyectoModulo || '';

				// Handle Sprint association
				setupSprintAssociation(tareaId, editarTareaButton);

				// Clear previous errors
				document.getElementById('editar-tarea-error').style.display = 'none';
				document.getElementById('editar-modulo-error').style.display = 'none';
				document.getElementById('editar-descripcion-error').style.display = 'none';

				// Reset form validation state
				const form = document.getElementById('editar-tarea-form');
				form.classList.remove('was-validated');
				const inputs = form.querySelectorAll('.is-invalid');
				inputs.forEach(input => input.classList.remove('is-invalid'));
			}
		});
	}

	// Initialize child task visibility (collapsed by default)
	const toggleButtons = document.querySelectorAll('.btn-toggle-hijas');
	toggleButtons.forEach(button => {
		const parentId = button.dataset.parentId;
		const childRows = document.querySelectorAll(`tr.tarea-hija[data-parent-id="${parentId}"]`);
		const icon = button.querySelector('i');

		// Start collapsed
		button.classList.add('collapsed');
		icon.className = 'fa fa-chevron-right';

		// Hide child rows initially
		childRows.forEach(row => {
			row.style.display = 'none';
		});
	});

	// --- AJAX Action Function ---
	function performAjaxAction(action, tareaId) {
		// Store current expanded state before making the request
		const expandedStates = getExpandedStates();

		const formData = new FormData();
		formData.append('action', action);
		formData.append('id', tareaId); // For AJAX requests, use 'id' parameter
		formData.append('is_ajax', '1');

		// Add current filter state to maintain context
		<?php if ($filtro_estado): ?>
		formData.append('estado', '<?php echo $filtro_estado; ?>');
		<?php endif; ?>

		fetch('ltareas', {
			method: 'POST',
			body: formData,
			headers: {
				'X-Requested-With': 'XMLHttpRequest'
			}
		})
		.then(response => {
			if (!response.ok) {
				throw new Error(`HTTP error! Status: ${response.status}`);
			}
			return response.json();
		})
		.then(data => {
			if (data.success) {
				// Use different notification methods based on action type
				if (action === 'marcar_terminada' || action === 'marcar_en_progreso' || action === 'eliminar') {
					// Use toast notification for status changes and deletions
					showToastNotification(data.message, 'success');
				} else {
					// Use SweetAlert for other operations
					swal({
						title: "Éxito",
						text: data.message,
						icon: "success",
						button: {
							text: "Cerrar",
							value: true,
							visible: true,
							className: "btn-success",
							closeModal: true
						}
					});
				}

				// Update UI based on action
				if (data.action === 'eliminar') {
					// Remove the task row from the table
					removeTaskFromUI(data.tarea_id);
				} else {
					// Update task status in the UI
					updateTaskStatusInUI(data.tarea_id, data.updated_tarea);
				}

				// Restore expanded states after UI update
				setTimeout(() => {
					// Convert old format to new format for compatibility
					const convertedStates = {};
					Object.keys(expandedStates).forEach(parentId => {
						convertedStates[`main_${parentId}`] = expandedStates[parentId];
					});
					restoreExpandedStates(convertedStates);
				}, 100);

			} else {
				// Always use SweetAlert for errors
				swal({
					title: "Error",
					text: data.message || 'Ocurrió un error inesperado.',
					icon: "error",
					button: {
						text: "Cerrar",
						value: true,
						visible: true,
						className: "btn-danger",
						closeModal: true
					}
				});
			}
		})
		.catch(error => {
			console.error('Error in AJAX action:', error);
			swal({
				title: "Error de comunicación",
				text: 'Error de red al procesar la acción: ' + error.message,
				icon: "error",
				button: {
					text: "Cerrar",
					value: true,
					visible: true,
					className: "btn-danger",
					closeModal: true
				}
			});
		});
	}



	// --- UI Update Functions ---
	function removeTaskFromUI(tareaId) {
		const taskRow = document.querySelector(`tr[data-tarea-id="${tareaId}"]`);
		if (taskRow) {
			// If it's a parent task, also remove its children
			if (taskRow.classList.contains('tarea-padre')) {
				const childRows = document.querySelectorAll(`tr.tarea-hija[data-parent-id="${tareaId}"]`);
				childRows.forEach(childRow => {
					childRow.remove();
				});
			}
			taskRow.remove();
		}
	}

	function updateTaskStatusInUI(tareaId, updatedTarea) {
		if (!updatedTarea) return;

		const taskRow = document.querySelector(`tr[data-tarea-id="${tareaId}"]`);
		if (taskRow) {
			// Update status badge - specifically target the badge in the "Estado" column (5th td)
			// This avoids updating the wrong badge in case there are multiple badges in the row
			const statusCell = taskRow.querySelector('td:nth-child(5)'); // Estado column (5th column)
			const statusBadge = statusCell ? statusCell.querySelector('.badge:not(.badge-hijo-count)') : null;
			if (statusBadge) {
				statusBadge.className = `badge ${updatedTarea.bg_color}`;
				statusBadge.textContent = updatedTarea.nombre_estado;
			}

			// Update date if it's a completion
			if (updatedTarea.fecha_terminacion) {
				const dateCell = taskRow.querySelector('td:last-child');
				if (dateCell) {
					// Format the date (assuming it comes in a standard format)
					const formattedDate = formatDate(updatedTarea.fecha_terminacion);
					dateCell.textContent = formattedDate;
				}
			}

			// Update action buttons by re-including the actions partial
			// For now, we'll reload the page section or update buttons manually
			updateActionButtons(taskRow, updatedTarea);
		}
	}

	function updateActionButtons(taskRow, updatedTarea) {
		const actionsCell = taskRow.querySelector('td:first-child');
		if (!actionsCell) return;

		// Find and update specific buttons based on new status
		const marcarTerminadaBtn = actionsCell.querySelector('.btn-marcar-terminada');
		const marcarEnProgresoBtn = actionsCell.querySelector('.btn-marcar-en-progreso');

		// Hide/show buttons based on new status
		if (updatedTarea.estado === <?php echo Tarea::ESTADO_TERMINADO; ?>) {
			// Task is now completed - hide both status change buttons
			if (marcarTerminadaBtn) marcarTerminadaBtn.style.display = 'none';
			if (marcarEnProgresoBtn) marcarEnProgresoBtn.style.display = 'none';
		} else if (updatedTarea.estado === <?php echo Tarea::ESTADO_EN_PROGRESO; ?>) {
			// Task is now in progress - hide the "mark as in progress" button
			if (marcarEnProgresoBtn) marcarEnProgresoBtn.style.display = 'none';
			if (marcarTerminadaBtn) marcarTerminadaBtn.style.display = 'inline-block';
		}
	}

	function formatDate(dateString) {
		if (!dateString) return '';

		try {
			const date = new Date(dateString);
			const year = date.getFullYear();
			const month = String(date.getMonth() + 1).padStart(2, '0');
			const day = String(date.getDate()).padStart(2, '0');
			return `${year}-${month}-${day}`;
		} catch (e) {
			return dateString; // Return original if parsing fails
		}
	}

	// --- Toast Notification Functions ---
	function showToastNotification(message, type = 'success') {
		const toastContainer = document.getElementById('toast-container');
		if (!toastContainer) return;

		const toastId = 'toast-' + Date.now();
		const toastHtml = `
			<div class="toast toast-${type}" id="${toastId}" role="alert" aria-live="assertive" aria-atomic="true" data-bs-autohide="true" data-bs-delay="4000">
				<div class="toast-header">
					<i class="fa ${type === 'success' ? 'fa-check-circle text-success' : 'fa-exclamation-circle text-danger'} me-2"></i>
					<strong class="me-auto">${type === 'success' ? 'Éxito' : 'Error'}</strong>
					<button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
				</div>
				<div class="toast-body">
					${message}
				</div>
			</div>
		`;

		toastContainer.insertAdjacentHTML('beforeend', toastHtml);
		const toastElement = document.getElementById(toastId);
		const toast = new bootstrap.Toast(toastElement);
		toast.show();

		// Remove toast element after it's hidden
		toastElement.addEventListener('hidden.bs.toast', () => {
			toastElement.remove();
		});
	}

	// --- Agentes Modal Functions ---
	function loadAgentesDropdown() {
		return fetch('get_agentes_ajax')
			.then(response => {
				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}
				return response.json();
			})
			.then(data => {
				if (data.success) {
					const addSelect = document.getElementById('id_agente');
					const editSelect = document.getElementById('edit_id_agente');

					// Clear existing options
					addSelect.innerHTML = '<option value="">-- Seleccione un agente --</option>';
					editSelect.innerHTML = '<option value="">-- Seleccione un agente --</option>';

					// Add agentes to both dropdowns
					data.agentes.forEach(agente => {
						const option1 = document.createElement('option');
						option1.value = agente.id;
						option1.textContent = agente.descripcion;
						addSelect.appendChild(option1);

						const option2 = document.createElement('option');
						option2.value = agente.id;
						option2.textContent = agente.descripcion;
						editSelect.appendChild(option2);
					});
				} else {
					throw new Error(data.message || 'Error al cargar agentes');
				}
			});
	}

	function loadTareaAgentes(tareaId) {
		return fetch('ltareas', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
			body: `action=get_tarea_agentes&tarea_id=${tareaId}`
		})
		.then(response => {
			if (!response.ok) {
				throw new Error(`HTTP error! status: ${response.status}`);
			}
			return response.json();
		})
		.then(data => {
			if (data.success) {
				const agentes = data.agentes;
				const tableBody = document.getElementById('agentes-table-body');
				const emptyDiv = document.getElementById('agentes-empty');

				// Clear previous content
				tableBody.innerHTML = '';

				if (agentes.length === 0) {
					// Show empty state
					emptyDiv.style.display = 'block';
					tableBody.closest('.table-responsive').style.display = 'none';
				} else {
					// Hide empty state and show table
					emptyDiv.style.display = 'none';
					tableBody.closest('.table-responsive').style.display = 'block';

					// Populate table with agentes data
					agentes.forEach(agente => {
						addAgenteToTable(agente);
					});
				}
			} else {
				throw new Error(data.message || 'Error al cargar los agentes');
			}
		});
	}

	function addAgenteToTable(tareaAgente) {
		const tbody = document.getElementById('agentes-table-body');
		if (!tbody) return;

		// Remove the "No hay agentes" message if it exists
		const emptyDiv = document.getElementById('agentes-empty');
		if (emptyDiv) {
			emptyDiv.style.display = 'none';
			tbody.closest('.table-responsive').style.display = 'block';
		}

		const newRow = tbody.insertRow();
		newRow.dataset.id = tareaAgente.id;
		newRow.innerHTML = `
			<td class="text-center">
				<button type="button" class="btn btn-xs btn-primary me-1 btn-edit-agente"
				        title="Editar"
				        data-agente-id="${tareaAgente.id}">
					<i class="fa fa-edit"></i>
				</button>
				<button type="button" class="btn btn-xs btn-danger btn-delete-agente"
				        title="Eliminar"
				        data-agente-id="${tareaAgente.id}"
				        data-agente-nombre="${tareaAgente.agente_descripcion || tareaAgente.agente_nombre || 'N/A'}">
					<i class="fa fa-trash-alt"></i>
				</button>
			</td>
			<td class="agente-nombre">${tareaAgente.agente_descripcion || tareaAgente.agente_nombre || 'N/A'}</td>
			<td class="text-end costo-usd">$${parseFloat(tareaAgente.costo_usd || 0).toFixed(2)}</td>
			<td class="text-center n-mensajes">${tareaAgente.n_mensajes || 0}</td>
		`;
	}

	function updateAgenteInTable(tareaAgente) {
		const row = document.querySelector(`tr[data-id="${tareaAgente.id}"]`);
		if (row) {
			row.querySelector('.agente-nombre').textContent = tareaAgente.agente_nombre;
			row.querySelector('.costo-usd').textContent = `$${parseFloat(tareaAgente.costo_usd || 0).toFixed(2)}`;
			row.querySelector('.n-mensajes').textContent = tareaAgente.n_mensajes || 0;
			// Update data attributes for delete button
			const deleteBtn = row.querySelector('.btn-delete-agente');
			if (deleteBtn) {
				deleteBtn.setAttribute('data-agente-nombre', tareaAgente.agente_nombre);
			}
		}
	}

	function updateAgentesButtonState(tareaId) {
		// Get current count of agentes in the modal table
		const tableBody = document.getElementById('agentes-table-body');
		const agentesCount = tableBody ? tableBody.querySelectorAll('tr').length : 0;

		// Find the agentes button for this specific task
		const agentesButton = document.querySelector(`button.btn-ver-agentes[data-tareaid="${tareaId}"]`);

		if (agentesButton) {
			// Update button styling
			if (agentesCount > 0) {
				// Has agentes - use btn-info style
				agentesButton.classList.remove('btn-outline-info');
				agentesButton.classList.add('btn-info');

				// Update tooltip text with count
				agentesButton.title = `Gestionar Agentes Asociados (${agentesCount})`;
			} else {
				// No agentes - use btn-outline-info style
				agentesButton.classList.remove('btn-info');
				agentesButton.classList.add('btn-outline-info');

				// Update tooltip text without count
				agentesButton.title = 'Gestionar Agentes de la Tarea';
			}
		}
	}

	// --- Sprint Association Functions ---
	function setupSprintAssociation(tareaId, editarTareaButton) {
		const sprintContainer = document.getElementById('editar-sprint-association-container');
		const sprintCheckbox = document.getElementById('editar-sprint-association');
		const sprintText = document.getElementById('editar-sprint-association-text');
		const sprintHelp = document.getElementById('editar-sprint-current-name');

		// Get current task's sprint association from data attributes
		// Handle empty string, "null" string, and actual null values
		let tareaIdSprint = editarTareaButton.dataset.tareaidSprint;
		if (!tareaIdSprint || tareaIdSprint === 'null' || tareaIdSprint === '') {
			tareaIdSprint = null;
		} else {
			tareaIdSprint = parseInt(tareaIdSprint);
		}

		// Additional check: if the task is in the Sprint panel, it's definitely associated
		const isInSprintPanel = editarTareaButton.closest('#sprint-table-body') !== null;

		// Check if there's an active sprint
		fetch('ltareas', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
			body: 'action=get_active_sprint'
		})
		.then(response => response.json())
		.then(data => {
			if (data.success && data.has_active_sprint) {
				// There's an active sprint - show the association control
				sprintContainer.style.display = 'block';

				const activeSprint = data.sprint;

				// Determine if currently associated - use multiple checks for reliability
				let isCurrentlyAssociated = false;

				if (isInSprintPanel) {
					// If task is in Sprint panel, it's definitely associated with the active Sprint
					isCurrentlyAssociated = true;
				} else {
					// For tasks in main panel, check the Sprint ID
					isCurrentlyAssociated = tareaIdSprint !== null && tareaIdSprint === activeSprint.id;
				}

				// Set checkbox state
				sprintCheckbox.checked = isCurrentlyAssociated;

				// Update text based on current state
				if (isCurrentlyAssociated) {
					sprintText.textContent = 'Desasociar tarea del sprint actual';
					sprintHelp.textContent = `Actualmente asociada a: ${activeSprint.descripcion}`;
				} else {
					sprintText.textContent = 'Asociar tarea al sprint actual';
					sprintHelp.textContent = `Sprint disponible: ${activeSprint.descripcion}`;
				}

				// Store sprint ID for form submission
				sprintCheckbox.dataset.sprintId = activeSprint.id;
				sprintCheckbox.dataset.currentlyAssociated = isCurrentlyAssociated ? '1' : '0';
			} else {
				// No active sprint - hide the association control
				sprintContainer.style.display = 'none';
			}
		})
		.catch(error => {
			console.error('Error checking active sprint:', error);
			sprintContainer.style.display = 'none';
		});
	}

	// --- Modal Event Handlers ---
	const addAgenteForm = document.getElementById('add-agente-form');
	const editAgenteForm = document.getElementById('edit-agente-form');
	const agentesTableBody = document.getElementById('agentes-table-body');
	const addAgenteFeedback = document.getElementById('add-agente-feedback');
	const editAgenteError = document.getElementById('edit-agente-error');
	const editAgenteModal = new bootstrap.Modal(document.getElementById('editAgenteModal'));

	// Add agent form submission
	if (addAgenteForm) {
		addAgenteForm.addEventListener('submit', function(event) {
			event.preventDefault();

			let isValid = true;
			const agenteInput = document.getElementById('id_agente');
			const costoInput = document.getElementById('costo_usd');
			const mensajesInput = document.getElementById('n_mensajes');

			// Reset previous validation states
			[agenteInput, costoInput, mensajesInput].forEach(input => {
				if (input) input.classList.remove('is-invalid');
			});

			// Validate Agente selection
			if (agenteInput && agenteInput.value.trim() === '') {
				isValid = false;
				agenteInput.classList.add('is-invalid');
			}

			if (!isValid) {
				addAgenteFeedback.innerHTML = `<div class="alert alert-danger">Por favor, seleccione un agente.</div>`;
				return;
			}

			// Client-side validation passed, proceed with AJAX
			addAgenteFeedback.innerHTML = `<div class="alert alert-info">Asignando agente... <span class="spinner-border spinner-border-sm"></span></div>`;

			const formData = new FormData();
			formData.append('action', 'crear');
			formData.append('id_tarea', window.currentTareaId);
			formData.append('id_agente', agenteInput.value);
			formData.append('costo_usd', costoInput.value || 0);
			formData.append('n_mensajes', mensajesInput.value || 0);

			fetch('ltarea_agentes_ajax', {
				method: 'POST',
				body: formData,
				headers: {
					'X-Requested-With': 'XMLHttpRequest'
				}
			})
			.then(response => response.json())
			.then(data => {
				if (data.success) {
					// Success
					addAgenteFeedback.innerHTML = `<div class="alert alert-success alert-dismissible fade show" role="alert">
						${data.message}
						<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
					</div>`;
					addAgenteToTable(data.tarea_agente);
					addAgenteForm.reset();
					// Remove any lingering validation classes
					[agenteInput, costoInput, mensajesInput].forEach(input => {
						if (input) input.classList.remove('is-invalid');
					});
					// Update the agentes button state in the main task list
					updateAgentesButtonState(window.currentTareaId);
				} else {
					// Server-side validation or other error
					addAgenteFeedback.innerHTML = `<div class="alert alert-danger">${data.message || 'Ocurrió un error inesperado.'}</div>`;
				}
			})
			.catch(error => {
				console.error('Error:', error);
				addAgenteFeedback.innerHTML = `<div class="alert alert-danger">Error de comunicación. Inténtelo de nuevo.</div>`;
			});
		});
	}

	// Edit agent form submission
	if (editAgenteForm) {
		editAgenteForm.addEventListener('submit', function(event) {
			event.preventDefault();

			const formData = new FormData();
			formData.append('action', 'modificar');
			formData.append('id', document.getElementById('edit-tarea-agente-id').value);
			formData.append('id_tarea', window.currentTareaId);
			formData.append('id_agente', document.getElementById('edit_id_agente').value);
			formData.append('costo_usd', document.getElementById('edit_costo_usd').value || 0);
			formData.append('n_mensajes', document.getElementById('edit_n_mensajes').value || 0);

			// Hide previous errors
			editAgenteError.style.display = 'none';

			fetch('ltarea_agentes_ajax', {
				method: 'POST',
				body: formData
			})
			.then(response => response.json())
			.then(data => {
				if (data.success) {
					// Update row in table
					updateAgenteInTable(data.tarea_agente);
					// Close modal
					editAgenteModal.hide();
					// Update the agentes button state in the main task list
					updateAgentesButtonState(window.currentTareaId);
				} else {
					editAgenteError.textContent = data.message || 'Error al actualizar agente.';
					editAgenteError.style.display = 'block';
				}
			})
			.catch(error => {
				console.error('Error:', error);
				editAgenteError.textContent = 'Error al actualizar agente. Inténtelo de nuevo.';
				editAgenteError.style.display = 'block';
			});
		});
	}

	// Handle edit and delete buttons in the agentes table
	if (agentesTableBody) {
		agentesTableBody.addEventListener('click', function(event) {
			const editButton = event.target.closest('.btn-edit-agente');
			const deleteButton = event.target.closest('.btn-delete-agente');

			if (editButton) {
				const id = editButton.dataset.agenteId;
				const row = editButton.closest('tr');

				if (row) {
					// Populate edit form
					document.getElementById('edit-tarea-agente-id').value = id;

					// Get current values from the row
					const agenteNombre = row.querySelector('.agente-nombre').textContent;
					const costoText = row.querySelector('.costo-usd').textContent.replace('$', '');
					const mensajes = row.querySelector('.n-mensajes').textContent;

					// Find the agente ID by name in the dropdown
					const editSelect = document.getElementById('edit_id_agente');
					for (let option of editSelect.options) {
						if (option.textContent === agenteNombre) {
							editSelect.value = option.value;
							break;
						}
					}

					document.getElementById('edit_costo_usd').value = costoText;
					document.getElementById('edit_n_mensajes').value = mensajes;

					// Hide previous errors
					editAgenteError.style.display = 'none';

					// Show modal
					editAgenteModal.show();
				}
			}

			if (deleteButton) {
				const id = deleteButton.dataset.agenteId;
				const agenteNombre = deleteButton.dataset.agenteNombre;

				swal({
					title: "Confirmar Eliminación",
					text: `¿Seguro que quieres eliminar la asignación del agente "${agenteNombre}"?`,
					icon: "warning",
					buttons: {
						cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
						confirm: {text: "Eliminar", value: true, visible: true, className: "btn-danger", closeModal: true}
					},
					dangerMode: true,
				}).then((willDelete) => {
					if (willDelete) {
						const formData = new FormData();
						formData.append('action', 'eliminar');
						formData.append('id', id);

						fetch('ltarea_agentes_ajax', {
							method: 'POST',
							body: formData,
							headers: { 'X-Requested-With': 'XMLHttpRequest' }
						})
						.then(response => response.json())
						.then(data => {
							if (data.success) {
								// Remove row from table
								const row = deleteButton.closest('tr');
								if (row) {
									row.remove();
								}

								// Check if table is now empty
								const remainingRows = agentesTableBody.querySelectorAll('tr');
								if (remainingRows.length === 0) {
									document.getElementById('agentes-empty').style.display = 'block';
									agentesTableBody.closest('.table-responsive').style.display = 'none';
								}

								// Update the agentes button state in the main task list
								updateAgentesButtonState(window.currentTareaId);

								showToastNotification(data.message, 'success');
							} else {
								swal("Error", data.message || 'Error al eliminar agente.', "error");
							}
						})
						.catch(error => {
							console.error('Error:', error);
							swal("Error", 'Error de comunicación al eliminar agente.', "error");
						});
					}
				});
			}
		});
	}

	// --- Edit Task Modal Functionality ---
	const editarTareaModal = new bootstrap.Modal(document.getElementById('editarTareaModal'));
	const editarTareaForm = document.getElementById('editar-tarea-form');
	const editarCreateModuloModal = new bootstrap.Modal(document.getElementById('editarCreateModuloModal'));
	const editarCreateModuloForm = document.getElementById('editar-create-modulo-form');

	// Module Autocomplete functionality for edit modal
	const editarModuloAutocomplete = document.getElementById('editar-modulo-autocomplete');
	const editarModuloHidden = document.getElementById('editar-modulo-hidden');
	const editarModuloLoading = document.getElementById('editar-modulo-loading');
	const editarModuloError = document.getElementById('editar-modulo-error');
	const editarProyectoHidden = document.getElementById('editar-tarea-id-proyecto');

	if (editarModuloAutocomplete && editarProyectoHidden) {
		let searchTimeout;
		let currentAbortController;

		// Initialize jQuery UI autocomplete
		$(editarModuloAutocomplete).autocomplete({
			minLength: 2,
			delay: 300,
			appendTo: '#editarTareaModal', // Append to modal to fix z-index issues
			position: { my: "left top", at: "left bottom", collision: "flip" }, // Better positioning
			source: function(request, response) {
				// Check if project is selected
				if (!editarProyectoHidden.value) {
					response([]);
					return;
				}

				// Clear previous timeout
				if (searchTimeout) {
					clearTimeout(searchTimeout);
				}

				// Abort previous request
				if (currentAbortController) {
					currentAbortController.abort();
				}

				// Create new AbortController for this request
				currentAbortController = new AbortController();

				// Show loading indicator
				editarModuloLoading.style.display = 'block';
				editarModuloError.style.display = 'none';

				// Set timeout for the search
				searchTimeout = setTimeout(() => {
					const formData = new FormData();
					formData.append('query', request.term);
					formData.append('id_proyecto', editarProyectoHidden.value);
					formData.append('limit', '10');

					fetch('search_modulos_ajax', {
						method: 'POST',
						body: formData,
						headers: {
							'X-Requested-With': 'XMLHttpRequest'
						},
						signal: currentAbortController.signal
					})
					.then(response => {
						if (!response.ok) {
							throw new Error(`HTTP error! status: ${response.status}`);
						}
						return response.json();
					})
					.then(data => {
						editarModuloLoading.style.display = 'none';

						if (data.success) {
							// Transform results for jQuery UI autocomplete
							const autocompleteResults = data.results.map(item => ({
								label: item.display_text,
								value: item.display_text,
								id: item.id,
								descripcion: item.descripcion
							}));
							response(autocompleteResults);
						} else {
							editarModuloError.textContent = data.message || 'Error al buscar módulos.';
							editarModuloError.style.display = 'block';
							response([]);
						}
					})
					.catch(error => {
						editarModuloLoading.style.display = 'none';
						if (error.name !== 'AbortError') {
							console.error('Error searching modules:', error);
							editarModuloError.textContent = 'Error al buscar módulos. Inténtelo de nuevo.';
							editarModuloError.style.display = 'block';
						}
						response([]);
					});
				}, 300);
			},
			select: function(event, ui) {
				// Set the hidden field value
				editarModuloHidden.value = ui.item.id;
				editarModuloError.style.display = 'none';
				return true;
			}
		});

		// Clear hidden field when input is manually cleared
		editarModuloAutocomplete.addEventListener('input', function() {
			if (this.value.trim() === '') {
				editarModuloHidden.value = '';
				editarModuloError.style.display = 'none';
			}
		});

		// Handle blur event to validate selection
		editarModuloAutocomplete.addEventListener('blur', function() {
			// If there's text but no ID selected, clear the field
			if (this.value.trim() !== '' && editarModuloHidden.value === '') {
				setTimeout(() => {
					// Only clear if autocomplete menu is not open
					if (!$(this).autocomplete('widget').is(':visible')) {
						this.value = '';
						editarModuloHidden.value = '';
					}
				}, 200);
			}
		});
	}

	// Handle "New Module" button click
	const editarBtnNuevoModulo = document.getElementById('editar-btn-nuevo-modulo');
	if (editarBtnNuevoModulo) {
		editarBtnNuevoModulo.addEventListener('click', function() {
			// Clear the create module form
			document.getElementById('editar-nuevo-modulo-descripcion').value = '';
			document.getElementById('editar-nuevo-modulo-error').style.display = 'none';

			// Show the create module modal
			editarCreateModuloModal.show();
		});
	}

	// Handle create module form submission
	if (editarCreateModuloForm) {
		editarCreateModuloForm.addEventListener('submit', function(event) {
			event.preventDefault();

			const descripcion = document.getElementById('editar-nuevo-modulo-descripcion').value.trim();
			const id_proyecto = editarProyectoHidden.value;

			// Validate form data
			if (!descripcion) {
				document.getElementById('editar-nuevo-modulo-error').textContent = 'La descripción es requerida.';
				document.getElementById('editar-nuevo-modulo-error').style.display = 'block';
				return;
			}

			// Hide error message
			document.getElementById('editar-nuevo-modulo-error').style.display = 'none';

			// Create form data for AJAX request
			const formData = new FormData();
			formData.append('action', 'crear');
			formData.append('id_proyecto', id_proyecto);
			formData.append('descripcion', descripcion);

			// Send AJAX request
			fetch('lproyectos_modulos_ajax', {
				method: 'POST',
				body: formData
			})
			.then(response => response.json())
			.then(data => {
				if (data.success) {
					// Set the values in the edit task form
					editarModuloAutocomplete.value = descripcion;
					editarModuloHidden.value = data.id;

					// Close the create module modal
					editarCreateModuloModal.hide();

					// Show success message
					showToastNotification('Módulo creado correctamente', 'success');
				} else {
					document.getElementById('editar-nuevo-modulo-error').textContent = data.message || 'Error al crear el módulo.';
					document.getElementById('editar-nuevo-modulo-error').style.display = 'block';
				}
			})
			.catch(error => {
				console.error('Error:', error);
				document.getElementById('editar-nuevo-modulo-error').textContent = 'Error de comunicación al crear el módulo.';
				document.getElementById('editar-nuevo-modulo-error').style.display = 'block';
			});
		});
	}

	// Handle edit task form submission
	if (editarTareaForm) {
		editarTareaForm.addEventListener('submit', function(event) {
			event.preventDefault();

			// Get form data
			const tareaId = document.getElementById('editar-tarea-id').value;
			const descripcion = document.getElementById('editar-tarea-descripcion').value.trim();
			const idProyectoModulo = document.getElementById('editar-modulo-hidden').value;

			// Client-side validation
			let isValid = true;

			// Reset previous validation states
			document.getElementById('editar-tarea-descripcion').classList.remove('is-invalid');
			document.getElementById('editar-descripcion-error').style.display = 'none';
			document.getElementById('editar-tarea-error').style.display = 'none';

			// Validate description
			if (!descripcion) {
				document.getElementById('editar-tarea-descripcion').classList.add('is-invalid');
				document.getElementById('editar-descripcion-error').style.display = 'block';
				isValid = false;
			}

			if (!isValid) {
				return;
			}

			// Disable submit button during request
			const submitButton = editarTareaForm.querySelector('button[type="submit"]');
			const originalText = submitButton.textContent;
			submitButton.disabled = true;
			submitButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Actualizando...';

			// Handle Sprint association
			const sprintCheckbox = document.getElementById('editar-sprint-association');
			let sprintAction = null;
			let sprintId = null;

			if (sprintCheckbox && sprintCheckbox.dataset.sprintId) {
				const isChecked = sprintCheckbox.checked;
				const wasAssociated = sprintCheckbox.dataset.currentlyAssociated === '1';
				sprintId = sprintCheckbox.dataset.sprintId;

				if (isChecked && !wasAssociated) {
					sprintAction = 'associate';
				} else if (!isChecked && wasAssociated) {
					sprintAction = 'disassociate';
				}


			}

			// Create form data for AJAX request
			const formData = new FormData();
			formData.append('action', 'editar');
			formData.append('id', tareaId);
			formData.append('descripcion', descripcion);
			formData.append('id_proyecto_modulo', idProyectoModulo || '');
			formData.append('is_ajax', '1');

			// Add Sprint association data if needed
			if (sprintAction && sprintId) {
				formData.append('sprint_action', sprintAction);
				formData.append('sprint_id', sprintId);
			}

			// Send AJAX request
			fetch('ltareas', {
				method: 'POST',
				body: formData,
				headers: {
					'X-Requested-With': 'XMLHttpRequest'
				}
			})
			.then(response => {
				if (!response.ok) {
					throw new Error(`HTTP error! Status: ${response.status}`);
				}
				return response.json();
			})
			.then(data => {
				if (data.success) {
					// Close the modal
					editarTareaModal.hide();

					// Show success message
					showToastNotification(data.message, 'success');

					// Check if Sprint association changed - if so, refresh both task lists
					if (data.sprint_association_changed) {
						refreshTaskLists();
					} else {
						// Update the task in the UI using the old method for non-Sprint changes
						updateTaskInUI(tareaId, data.updated_tarea);
					}
				} else {
					document.getElementById('editar-tarea-error').textContent = data.message || 'Error al actualizar la tarea.';
					document.getElementById('editar-tarea-error').style.display = 'block';
				}
			})
			.catch(error => {
				console.error('Error:', error);
				document.getElementById('editar-tarea-error').textContent = 'Error de comunicación al actualizar la tarea.';
				document.getElementById('editar-tarea-error').style.display = 'block';
			})
			.finally(() => {
				// Re-enable submit button
				submitButton.disabled = false;
				submitButton.textContent = originalText;
			});
		});
	}

	// Function to update task in UI after edit
	function updateTaskInUI(tareaId, updatedTarea) {
		if (!updatedTarea) return;

		// Check if Sprint association changed
		const mainTableRow = document.querySelector('#tarea-table-body tr[data-tarea-id="' + tareaId + '"]');
		const sprintTableRow = document.querySelector('#sprint-table-body tr[data-tarea-id="' + tareaId + '"]');

		// Determine if task should be in Sprint table based on updated data
		const shouldBeInSprint = updatedTarea.id_sprint && updatedTarea.id_sprint !== null && updatedTarea.id_sprint !== '';

		// Handle Sprint association changes
		if (shouldBeInSprint && !sprintTableRow) {
			// Task was associated with Sprint but not in Sprint table - add it dynamically
			addTaskToSprintTable(updatedTarea);
		} else if (!shouldBeInSprint && sprintTableRow) {
			// Task was disassociated from Sprint - remove from Sprint table
			sprintTableRow.remove();

			// Check if Sprint table is now empty and show empty message
			checkAndShowSprintEmptyState();
		}

		// Update task in main table (always present)
		updateTaskRowInTable(tareaId, updatedTarea, '#tarea-table-body');

		// Update task in sprint table if it exists and should be there
		if (shouldBeInSprint) {
			updateTaskRowInTable(tareaId, updatedTarea, '#sprint-table-body');
		}
	}

	// Function to add a task to the Sprint table dynamically
	function addTaskToSprintTable(updatedTarea) {
		const sprintTableBody = document.getElementById('sprint-table-body');
		if (!sprintTableBody) return;

		// Remove empty state message if it exists
		const emptyRow = sprintTableBody.querySelector('tr td[colspan="6"]');
		if (emptyRow) {
			emptyRow.closest('tr').remove();
		}

		// Create the task row HTML
		const taskRow = document.createElement('tr');
		taskRow.setAttribute('data-tarea-id', updatedTarea.id);
		taskRow.className = 'tarea-padre'; // Assuming it's a parent task for now

		// Generate action buttons HTML (simplified version)
		const actionsHTML = `
			<button type="button" class="btn btn-xs btn-primary me-1 btn-editar-tarea"
			        title="Editar Tarea"
			        data-bs-toggle="modal"
			        data-bs-target="#editarTareaModal"
			        data-tareaid="${updatedTarea.id}"
			        data-tareadescripcion="${escapeHtml(updatedTarea.descripcion || '')}"
			        data-tareaidproyecto="${updatedTarea.id_proyecto || ''}"
			        data-tareaidproyectomodulo="${updatedTarea.id_proyecto_modulo || ''}"
			        data-tareanombreproyectomodulo="${escapeHtml(updatedTarea.nombre_proyecto_modulo || '')}"
			        data-tareaidSprint="${updatedTarea.id_sprint || ''}">
			    <i class="fa fa-edit"></i>
			</button>
			<button type="button" class="btn btn-xs btn-outline-info me-1 btn-ver-agentes"
			        title="Gestionar Agentes de la Tarea"
			        data-bs-toggle="modal"
			        data-bs-target="#gestionarAgentesModal"
			        data-tareaid="${updatedTarea.id}"
			        data-tareadescripcion="${escapeHtml(updatedTarea.descripcion || '')}">
			    <i class="fa fa-users"></i>
			</button>
			<button type="button" class="btn btn-xs btn-success me-1 btn-marcar-terminada"
			        title="Marcar como Terminada"
			        data-tareaid="${updatedTarea.id}">
			    <i class="fa fa-check"></i>
			</button>
			<button type="button" class="btn btn-xs btn-info me-1 btn-marcar-en-progreso"
			        title="Marcar como En Progreso"
			        data-tareaid="${updatedTarea.id}">
			    <i class="fa fa-play"></i>
			</button>
			<button type="button" class="btn btn-xs btn-danger btn-eliminar-tarea"
			        title="Eliminar Tarea"
			        data-tareaid="${updatedTarea.id}">
			    <i class="fa fa-trash-alt"></i>
			</button>
		`;

		taskRow.innerHTML = `
			<td class="text-center">${actionsHTML}</td>
			<td class="text-center">${updatedTarea.id}</td>
			<td>${escapeHtml(updatedTarea.nombre_proyecto_modulo || 'N/A')}</td>
			<td>${escapeHtml(updatedTarea.descripcion || 'N/A')}</td>
			<td class="text-center"><span class="badge ${updatedTarea.bg_color || 'bg-secondary'}">${escapeHtml(updatedTarea.nombre_estado || 'N/A')}</span></td>
			<td class="text-center">${updatedTarea.fecha_terminacion || ''}</td>
		`;

		// Add the row to the Sprint table
		sprintTableBody.appendChild(taskRow);
	}

	// Function to check if Sprint table is empty and show empty state
	function checkAndShowSprintEmptyState() {
		const sprintTableBody = document.getElementById('sprint-table-body');
		if (!sprintTableBody) return;

		const taskRows = sprintTableBody.querySelectorAll('tr[data-tarea-id]');
		if (taskRows.length === 0) {
			// No tasks left, show empty state
			const emptyRow = document.createElement('tr');
			emptyRow.innerHTML = `
				<td colspan="6" class="text-center py-4">
					<i class="fa fa-rocket fa-2x text-muted mb-2"></i>
					<p class="text-muted mb-0">No hay tareas asociadas al sprint activo</p>
				</td>
			`;
			sprintTableBody.appendChild(emptyRow);
		}
	}

	// Helper function to escape HTML
	function escapeHtml(text) {
		const div = document.createElement('div');
		div.textContent = text;
		return div.innerHTML;
	}

	// Function to refresh both task lists after Sprint association changes
	function refreshTaskLists() {
		// Store current expanded states before refresh
		const expandedStates = getExpandedStates();

		// Get current filter parameters
		const filtroProyectoId = <?php echo json_encode($filtro_proyecto_id); ?>;
		const filtroEstado = <?php echo json_encode($filtro_estado); ?>;

		if (!filtroProyectoId) {
			console.error('No project filter available for refresh');
			return;
		}

		// Prepare form data for refresh request
		const formData = new FormData();
		formData.append('action', 'refresh_task_lists');
		formData.append('filtro_proyecto_id', filtroProyectoId);
		if (filtroEstado) {
			formData.append('filtro_estado', filtroEstado);
		}
		formData.append('expanded_states', JSON.stringify(expandedStates));
		formData.append('is_ajax', '1');

		// Send AJAX request to refresh both task lists
		fetch('ltareas', {
			method: 'POST',
			body: formData,
			headers: {
				'X-Requested-With': 'XMLHttpRequest'
			}
		})
		.then(response => {
			if (!response.ok) {
				throw new Error(`HTTP error! Status: ${response.status}`);
			}
			return response.json();
		})
		.then(data => {
			if (data.success) {
				// Update main task table body
				const mainTableBody = document.getElementById('tarea-table-body');
				if (mainTableBody) {
					mainTableBody.innerHTML = data.main_table_html;
				}

				// Update Sprint task table body
				const sprintTableBody = document.getElementById('sprint-table-body');
				if (sprintTableBody) {
					sprintTableBody.innerHTML = data.sprint_table_html;
				}

				// Initialize both panels' child task visibility (collapsed by default) BEFORE restoring states
				initializeMainPanelToggleButtons();
				initializeSprintPanelToggleButtons();

				// Restore expanded states (this will override the default collapsed state for expanded parents)
				restoreExpandedStates(data.expanded_states || expandedStates);
			} else {
				console.error('Error refreshing task lists:', data.message);
				showToastNotification('Error al actualizar las listas de tareas', 'error');
			}
		})
		.catch(error => {
			console.error('Error refreshing task lists:', error);
			showToastNotification('Error de comunicación al actualizar las listas', 'error');
		});
	}

	// Function to get current expanded states of parent tasks
	function getExpandedStates() {
		const expandedStates = {};

		// Check main table
		const mainToggleButtons = document.querySelectorAll('#tarea-table-body .btn-toggle-hijas');
		mainToggleButtons.forEach(button => {
			const parentId = button.dataset.parentId;
			const isExpanded = !button.classList.contains('collapsed');
			expandedStates[`main_${parentId}`] = isExpanded;
		});

		// Check Sprint table
		const sprintToggleButtons = document.querySelectorAll('#sprint-table-body .btn-toggle-hijas');
		sprintToggleButtons.forEach(button => {
			const parentId = button.dataset.parentId;
			const isExpanded = !button.classList.contains('collapsed');
			expandedStates[`sprint_${parentId}`] = isExpanded;
		});

		return expandedStates;
	}

	// Function to restore expanded states after refresh
	function restoreExpandedStates(expandedStates) {
		if (!expandedStates || typeof expandedStates !== 'object') {
			return;
		}

		// Restore main table states
		Object.keys(expandedStates).forEach(key => {
			if (key.startsWith('main_')) {
				const parentId = key.replace('main_', '');
				const isExpanded = expandedStates[key];
				const toggleButton = document.querySelector(`#tarea-table-body .btn-toggle-hijas[data-parent-id="${parentId}"]`);

				if (toggleButton && isExpanded) {
					// Expand this parent's children
					const childRows = document.querySelectorAll(`#tarea-table-body tr.tarea-hija[data-parent-id="${parentId}"]`);
					const icon = toggleButton.querySelector('i');

					toggleButton.classList.remove('collapsed');
					icon.className = 'fa fa-chevron-down';
					childRows.forEach(row => {
						row.style.display = '';
					});
				}
			} else if (key.startsWith('sprint_')) {
				const parentId = key.replace('sprint_', '');
				const isExpanded = expandedStates[key];
				const toggleButton = document.querySelector(`#sprint-table-body .btn-toggle-hijas[data-parent-id="${parentId}"]`);

				if (toggleButton && isExpanded) {
					// Expand this parent's children
					const childRows = document.querySelectorAll(`#sprint-table-body tr.tarea-hija[data-parent-id="${parentId}"]`);
					const icon = toggleButton.querySelector('i');

					toggleButton.classList.remove('collapsed');
					icon.className = 'fa fa-chevron-down';
					childRows.forEach(row => {
						row.style.display = '';
					});
				}
			}
		});
	}

	// Function to initialize main panel toggle buttons after refresh
	function initializeMainPanelToggleButtons() {
		const mainToggleButtons = document.querySelectorAll('#tarea-table-body .btn-toggle-hijas');
		mainToggleButtons.forEach(button => {
			const parentId = button.dataset.parentId;
			const childRows = document.querySelectorAll(`#tarea-table-body tr.tarea-hija[data-parent-id="${parentId}"]`);
			const icon = button.querySelector('i');

			// Set default collapsed state for all buttons (will be overridden by restoreExpandedStates if needed)
			button.classList.add('collapsed');
			icon.className = 'fa fa-chevron-right';

			// Hide all child rows by default
			childRows.forEach(row => {
				row.style.display = 'none';
			});
		});
	}

	// Function to initialize Sprint panel toggle buttons after refresh
	function initializeSprintPanelToggleButtons() {
		const sprintToggleButtons = document.querySelectorAll('#sprint-table-body .btn-toggle-hijas');
		sprintToggleButtons.forEach(button => {
			const parentId = button.dataset.parentId;
			const childRows = document.querySelectorAll(`#sprint-table-body tr.tarea-hija[data-parent-id="${parentId}"]`);
			const icon = button.querySelector('i');

			// Set default collapsed state for all buttons (will be overridden by restoreExpandedStates if needed)
			button.classList.add('collapsed');
			icon.className = 'fa fa-chevron-right';

			// Hide all child rows by default
			childRows.forEach(row => {
				row.style.display = 'none';
			});
		});
	}

	// Helper function to update a task row in a specific table
	function updateTaskRowInTable(tareaId, updatedTarea, tableSelector) {
		const tableBody = document.querySelector(tableSelector);
		if (!tableBody) return;

		const taskRow = tableBody.querySelector(`tr[data-tarea-id="${tareaId}"]`);
		if (taskRow) {
			// Update description in the task description column
			// For both tables it's 4th column (after actions, #, modulo)
			const descriptionCell = taskRow.querySelector('td:nth-child(4)');
			if (descriptionCell) {
				// Preserve any existing child elements (like toggle buttons and badges)
				const existingElements = descriptionCell.querySelectorAll('button, .badge');
				const toggleButton = descriptionCell.querySelector('.btn-toggle-hijas');
				const badge = descriptionCell.querySelector('.badge-hijo-count');

				if (toggleButton) {
					// For parent tasks with children, preserve the toggle button and badge
					descriptionCell.innerHTML = '';
					descriptionCell.appendChild(toggleButton);
					descriptionCell.appendChild(document.createTextNode(' ' + (updatedTarea.descripcion || 'N/A')));
					if (badge) {
						descriptionCell.appendChild(document.createTextNode(' '));
						descriptionCell.appendChild(badge);
					}
				} else {
					// For regular tasks or child tasks
					const isChildTask = taskRow.classList.contains('tarea-hija');
					if (isChildTask) {
						// Child task - preserve the descripcion-hija div structure
						const descripcionDiv = descriptionCell.querySelector('.descripcion-hija');
						if (descripcionDiv) {
							descripcionDiv.textContent = updatedTarea.descripcion || 'N/A';
						} else {
							descriptionCell.innerHTML = `<div class="descripcion-hija">${updatedTarea.descripcion || 'N/A'}</div>`;
						}
					} else {
						// Regular task without children
						descriptionCell.textContent = updatedTarea.descripcion || 'N/A';
					}
				}
			}

			// Update module in the module column (3rd td)
			const moduleCell = taskRow.querySelector('td:nth-child(3)');
			if (moduleCell) {
				moduleCell.textContent = updatedTarea.nombre_proyecto_modulo || 'N/A';
			}

			// Update the edit button data attributes
			const editButton = taskRow.querySelector('.btn-editar-tarea');
			if (editButton) {
				editButton.dataset.tareadescripcion = updatedTarea.descripcion;
				editButton.dataset.tareaidproyectomodulo = updatedTarea.id_proyecto_modulo || '';
				editButton.dataset.tareanombreproyectomodulo = updatedTarea.nombre_proyecto_modulo || '';
				// Update sprint association data if available
				if (updatedTarea.id_sprint !== undefined) {
					editButton.dataset.tareaidSprint = updatedTarea.id_sprint || '';
				}
			}
		}
	}

	// --- Sprint Panel JavaScript ---

	// Sprint Creation Modal
	const createSprintModal = new bootstrap.Modal(document.getElementById('createSprintModal'));
	const createSprintForm = document.getElementById('create-sprint-form');
	const editSprintFieldModal = new bootstrap.Modal(document.getElementById('editSprintFieldModal'));
	const editSprintFieldForm = document.getElementById('edit-sprint-field-form');

	// Create Sprint button handler
	const btnCreateSprint = document.getElementById('btn-create-sprint');
	if (btnCreateSprint) {
		btnCreateSprint.addEventListener('click', function() {
			// Pre-populate description with current date
			const today = new Date();
			const dateStr = today.getFullYear() + '-' +
				String(today.getMonth() + 1).padStart(2, '0') + '-' +
				String(today.getDate()).padStart(2, '0');

			document.getElementById('sprint-descripcion').value = `Sprint ${dateStr}`;
			document.getElementById('create-sprint-error').style.display = 'none';

			createSprintModal.show();
		});
	}

	// Create Sprint form submission
	if (createSprintForm) {
		createSprintForm.addEventListener('submit', function(event) {
			event.preventDefault();

			const descripcion = document.getElementById('sprint-descripcion').value.trim();
			const errorDiv = document.getElementById('create-sprint-error');

			if (!descripcion) {
				errorDiv.textContent = 'La descripción es requerida';
				errorDiv.style.display = 'block';
				return;
			}

			// Disable submit button
			const submitBtn = createSprintForm.querySelector('button[type="submit"]');
			const originalText = submitBtn.textContent;
			submitBtn.disabled = true;
			submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Creando...';

			// Send AJAX request
			const formData = new FormData();
			formData.append('action', 'create_sprint');
			formData.append('descripcion', descripcion);

			fetch('ltareas', {
				method: 'POST',
				body: formData,
				headers: {
					'X-Requested-With': 'XMLHttpRequest'
				}
			})
			.then(response => response.json())
			.then(data => {
				if (data.success) {
					// Close modal and reload page to show new sprint
					createSprintModal.hide();
					showToastNotification(data.message, 'success');

					// Reload page to update sprint panel
					setTimeout(() => {
						window.location.reload();
					}, 1000);
				} else {
					errorDiv.textContent = data.message;
					errorDiv.style.display = 'block';
				}
			})
			.catch(error => {
				console.error('Error creating sprint:', error);
				errorDiv.textContent = 'Error de comunicación al crear el sprint';
				errorDiv.style.display = 'block';
			})
			.finally(() => {
				submitBtn.disabled = false;
				submitBtn.textContent = originalText;
			});
		});
	}

	// Function to reattach Sprint button event listeners after DOM manipulation
	function reattachSprintButtonListeners() {
		// Remove existing listeners by cloning and replacing elements
		const btnEditSprintDescripcion = document.getElementById('btn-edit-sprint-descripcion');
		const btnEditSprintCambiosBd = document.getElementById('btn-edit-sprint-cambios-bd');
		const btnEditSprintCambiosResources = document.getElementById('btn-edit-sprint-cambios-resources');
		const btnFinalizeSprint = document.getElementById('btn-finalize-sprint');

		// Re-attach event listeners
		if (btnEditSprintDescripcion) {
			btnEditSprintDescripcion.addEventListener('click', function() {
				openSprintFieldEditModal('descripcion', this.dataset.sprintId);
			});
		}

		if (btnEditSprintCambiosBd) {
			btnEditSprintCambiosBd.addEventListener('click', function() {
				openSprintFieldEditModal('cambios_bd', this.dataset.sprintId);
			});
		}

		if (btnEditSprintCambiosResources) {
			btnEditSprintCambiosResources.addEventListener('click', function() {
				openSprintFieldEditModal('cambios_resources', this.dataset.sprintId);
			});
		}

		if (btnFinalizeSprint) {
			btnFinalizeSprint.addEventListener('click', function() {
				const sprintId = this.dataset.sprintId;

				swal({
					title: "Finalizar Sprint",
					text: "¿Estás seguro de que quieres finalizar este sprint? Esta acción no se puede deshacer.",
					icon: "warning",
					buttons: {
						cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
						confirm: {text: "Finalizar Sprint", value: true, visible: true, className: "btn-danger", closeModal: true}
					},
					dangerMode: true,
				}).then((willFinalize) => {
					if (willFinalize) {
						finalizeSprintAjax(sprintId);
					}
				});
			});
		}
	}

	// Sprint field edit buttons - Initial setup
	const btnEditSprintDescripcion = document.getElementById('btn-edit-sprint-descripcion');
	const btnEditSprintCambiosBd = document.getElementById('btn-edit-sprint-cambios-bd');
	const btnEditSprintCambiosResources = document.getElementById('btn-edit-sprint-cambios-resources');
	const btnFinalizeSprint = document.getElementById('btn-finalize-sprint');

	// Initialize Sprint button event listeners
	reattachSprintButtonListeners();

	// Sprint field edit modal functions
	function openSprintFieldEditModal(field, sprintId) {
		document.getElementById('edit-sprint-id').value = sprintId;
		document.getElementById('edit-sprint-field').value = field;

		// Hide all field containers
		document.getElementById('descripcion-field').style.display = 'none';
		document.getElementById('cambios-bd-field').style.display = 'none';
		document.getElementById('cambios-resources-field').style.display = 'none';

		// Show the appropriate field and set modal title
		const modalTitle = document.getElementById('editSprintFieldModalLabel');

		if (field === 'descripcion') {
			document.getElementById('descripcion-field').style.display = 'block';
			modalTitle.textContent = 'Editar Descripción del Sprint';
			// Get current value from the sprint panel title - extract only the text content between icon and buttons
			const titleElement = document.querySelector('#sprint-panel .panel-title');
			if (titleElement) {
				// Get the text content and extract just the description part
				const fullText = titleElement.textContent || titleElement.innerText || '';
				const match = fullText.match(/Sprint Activo:\s*(.+?)(?:\s*Descripción|$)/);
				const currentDesc = match ? match[1].trim() : '';
				document.getElementById('edit-sprint-descripcion').value = currentDesc;
			}
			document.getElementById('edit-sprint-field-error').style.display = 'none';
			editSprintFieldModal.show();
		} else if (field === 'cambios_bd') {
			document.getElementById('cambios-bd-field').style.display = 'block';
			modalTitle.textContent = 'Editar Cambios en Base de Datos';
			// Fetch current sprint data to populate the field
			fetchSprintDataAndPopulateField(sprintId, field);
		} else if (field === 'cambios_resources') {
			document.getElementById('cambios-resources-field').style.display = 'block';
			modalTitle.textContent = 'Editar Cambios en Resources';
			// Fetch current sprint data to populate the field
			fetchSprintDataAndPopulateField(sprintId, field);
		}
	}

	// Function to fetch sprint data and populate modal fields
	function fetchSprintDataAndPopulateField(sprintId, field) {
		// Show loading state
		const fieldInput = document.getElementById(`edit-sprint-${field.replace('_', '-')}`);
		if (fieldInput) {
			fieldInput.value = 'Cargando...';
			fieldInput.disabled = true;
		}

		// Fetch sprint data
		fetch('ltareas', {
			method: 'POST',
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded',
			},
			body: 'action=get_active_sprint'
		})
		.then(response => response.json())
		.then(data => {
			if (data.success && data.has_active_sprint && data.sprint) {
				const sprint = data.sprint;
				if (fieldInput) {
					// Populate with the actual field value
					if (field === 'cambios_bd') {
						fieldInput.value = sprint.cambios_bd || '';
					} else if (field === 'cambios_resources') {
						fieldInput.value = sprint.cambios_resources || '';
					}
					fieldInput.disabled = false;
				}
			} else {
				if (fieldInput) {
					fieldInput.value = '';
					fieldInput.disabled = false;
				}
			}
			document.getElementById('edit-sprint-field-error').style.display = 'none';
			editSprintFieldModal.show();
		})
		.catch(error => {
			console.error('Error fetching sprint data:', error);
			if (fieldInput) {
				fieldInput.value = '';
				fieldInput.disabled = false;
			}
			document.getElementById('edit-sprint-field-error').style.display = 'none';
			editSprintFieldModal.show();
		});
	}

	// Sprint field edit form submission
	if (editSprintFieldForm) {
		editSprintFieldForm.addEventListener('submit', function(event) {
			event.preventDefault();

			const sprintId = document.getElementById('edit-sprint-id').value;
			const field = document.getElementById('edit-sprint-field').value;
			let value = '';

			if (field === 'descripcion') {
				value = document.getElementById('edit-sprint-descripcion').value.trim();
			} else if (field === 'cambios_bd') {
				value = document.getElementById('edit-sprint-cambios-bd').value.trim();
			} else if (field === 'cambios_resources') {
				value = document.getElementById('edit-sprint-cambios-resources').value.trim();
			}

			// Disable submit button
			const submitBtn = editSprintFieldForm.querySelector('button[type="submit"]');
			const originalText = submitBtn.textContent;
			submitBtn.disabled = true;
			submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Actualizando...';

			// Send AJAX request
			const formData = new FormData();
			formData.append('action', 'edit_sprint_field');
			formData.append('sprint_id', sprintId);
			formData.append('field', field);
			formData.append('value', value);

			fetch('ltareas', {
				method: 'POST',
				body: formData,
				headers: {
					'X-Requested-With': 'XMLHttpRequest'
				}
			})
			.then(response => response.json())
			.then(data => {
				if (data.success) {
					editSprintFieldModal.hide();
					showToastNotification(data.message, 'success');

					// Update UI if it's description field
					if (field === 'descripcion') {
						const titleElement = document.querySelector('#sprint-panel .panel-title');
						if (titleElement) {
							// Preserve the buttons container but update only the text content
							const buttonsContainer = titleElement.querySelector('.float-end');
							const buttonsHTML = buttonsContainer ? buttonsContainer.outerHTML : '';

							// Update only the text content, preserving the icon and buttons
							titleElement.innerHTML = `<i class="fa fa-rocket me-2"></i>Sprint Activo: ${value}${buttonsHTML ? ' ' + buttonsHTML : ''}`;

							// Re-attach event listeners to the buttons since innerHTML replacement removes them
							reattachSprintButtonListeners();
						}
					}
				} else {
					const errorDiv = document.getElementById('edit-sprint-field-error');
					errorDiv.textContent = data.message;
					errorDiv.style.display = 'block';
				}
			})
			.catch(error => {
				console.error('Error updating sprint field:', error);
				const errorDiv = document.getElementById('edit-sprint-field-error');
				errorDiv.textContent = 'Error de comunicación al actualizar el campo';
				errorDiv.style.display = 'block';
			})
			.finally(() => {
				submitBtn.disabled = false;
				submitBtn.textContent = originalText;
			});
		});
	}

	// Finalize sprint AJAX function
	function finalizeSprintAjax(sprintId) {
		const formData = new FormData();
		formData.append('action', 'finalize_sprint');
		formData.append('sprint_id', sprintId);

		fetch('ltareas', {
			method: 'POST',
			body: formData,
			headers: {
				'X-Requested-With': 'XMLHttpRequest'
			}
		})
		.then(response => response.json())
		.then(data => {
			if (data.success) {
				swal({
					title: "Sprint Finalizado",
					text: data.message,
					icon: "success",
					button: {
						text: "Cerrar",
						value: true,
						visible: true,
						className: "btn-success",
						closeModal: true
					}
				}).then(() => {
					// Reload page to update sprint panel
					window.location.reload();
				});
			} else {
				swal({
					title: "Error",
					text: data.message,
					icon: "error",
					button: {
						text: "Cerrar",
						value: true,
						visible: true,
						className: "btn-danger",
						closeModal: true
					}
				});
			}
		})
		.catch(error => {
			console.error('Error finalizing sprint:', error);
			swal({
				title: "Error de Comunicación",
				text: "Error al finalizar el sprint. Inténtelo de nuevo.",
				icon: "error",
				button: {
					text: "Cerrar",
					value: true,
					visible: true,
					className: "btn-danger",
					closeModal: true
				}
			});
		});
	}

	// Handle Sprint panel task actions (same as main task list)
	const sprintTableBody = document.getElementById('sprint-table-body');
	if (sprintTableBody) {
		sprintTableBody.addEventListener('click', function(event) {
			// Use the same event handlers as the main task table
			const marcarTerminadaButton = event.target.closest('.btn-marcar-terminada');
			const marcarEnProgresoButton = event.target.closest('.btn-marcar-en-progreso');
			const eliminarButton = event.target.closest('.btn-eliminar-tarea');
			const verAgentesButton = event.target.closest('.btn-ver-agentes');
			const editarTareaButton = event.target.closest('.btn-editar-tarea');
			const toggleButton = event.target.closest('.btn-toggle-hijas');

			// Handle all the same actions as main table
			if (marcarTerminadaButton) {
				event.preventDefault();
				const tareaId = marcarTerminadaButton.dataset.tareaid;
				swal({
					title: "Confirmar Acción",
					text: "¿Seguro que quieres marcar esta tarea como terminada?",
					icon: "warning",
					buttons: {
						cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
						confirm: {text: "Marcar como Terminada", value: true, visible: true, className: "btn-success", closeModal: true}
					},
				}).then((willComplete) => {
					if (willComplete) {
						performAjaxAction('marcar_terminada', tareaId);
					}
				});
			}

			if (marcarEnProgresoButton) {
				event.preventDefault();
				const tareaId = marcarEnProgresoButton.dataset.tareaid;
				swal({
					title: "Confirmar Acción",
					text: "¿Seguro que quieres marcar esta tarea como en progreso?",
					icon: "warning",
					buttons: {
						cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
						confirm: {text: "Marcar como En Progreso", value: true, visible: true, className: "btn-info", closeModal: true}
					},
				}).then((willProgress) => {
					if (willProgress) {
						performAjaxAction('marcar_en_progreso', tareaId);
					}
				});
			}

			if (eliminarButton) {
				event.preventDefault();
				const tareaId = eliminarButton.dataset.tareaid;
				swal({
					title: "Confirmar Eliminación",
					text: "¿Seguro que quieres eliminar esta tarea? Esta acción no se puede deshacer.",
					icon: "warning",
					buttons: {
						cancel: {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
						confirm: {text: "Eliminar", value: true, visible: true, className: "btn-danger", closeModal: true}
					},
					dangerMode: true,
				}).then((willDelete) => {
					if (willDelete) {
						performAjaxAction('eliminar', tareaId);
					}
				});
			}

			if (toggleButton) {
				event.preventDefault();
				const parentId = toggleButton.dataset.parentId;
				const childRows = document.querySelectorAll(`#sprint-table-body tr.tarea-hija[data-parent-id="${parentId}"]`);
				const icon = toggleButton.querySelector('i');

				let isCollapsed = toggleButton.classList.contains('collapsed');

				childRows.forEach(row => {
					if (isCollapsed) {
						row.style.display = '';
					} else {
						row.style.display = 'none';
					}
				});

				if (isCollapsed) {
					toggleButton.classList.remove('collapsed');
					icon.className = 'fa fa-chevron-down';
				} else {
					toggleButton.classList.add('collapsed');
					icon.className = 'fa fa-chevron-right';
				}
			}

			if (verAgentesButton) {
				event.preventDefault();
				const tareaId = verAgentesButton.dataset.tareaid;
				const tareaDescripcion = verAgentesButton.dataset.tareadescripcion;

				window.currentTareaId = tareaId;
				document.getElementById('modal-tarea-descripcion').textContent = tareaDescripcion;

				document.getElementById('agentes-loading').style.display = 'block';
				document.getElementById('agentes-content').style.display = 'none';
				document.getElementById('agentes-error').style.display = 'none';

				Promise.all([
					loadAgentesDropdown(),
					loadTareaAgentes(tareaId)
				]).then(() => {
					document.getElementById('agentes-loading').style.display = 'none';
					document.getElementById('agentes-content').style.display = 'block';
				}).catch(error => {
					console.error('Error loading modal data:', error);
					document.getElementById('agentes-loading').style.display = 'none';
					document.getElementById('agentes-content').style.display = 'block';
					document.getElementById('agentes-error').textContent = 'Error al cargar los datos: ' + error.message;
					document.getElementById('agentes-error').style.display = 'block';
				});
			}

			if (editarTareaButton) {
				event.preventDefault();
				const tareaId = editarTareaButton.dataset.tareaid;
				const tareaDescripcion = editarTareaButton.dataset.tareadescripcion;
				const tareaIdProyecto = editarTareaButton.dataset.tareaidproyecto;
				const tareaIdProyectoModulo = editarTareaButton.dataset.tareaidproyectomodulo;
				const tareaNombreProyectoModulo = editarTareaButton.dataset.tareanombreproyectomodulo;

				document.getElementById('editar-tarea-id').value = tareaId;
				document.getElementById('editar-tarea-descripcion').value = tareaDescripcion;
				document.getElementById('editar-tarea-id-proyecto').value = tareaIdProyecto;
				document.getElementById('editar-modulo-hidden').value = tareaIdProyectoModulo || '';
				document.getElementById('editar-modulo-autocomplete').value = tareaNombreProyectoModulo || '';

				setupSprintAssociation(tareaId, editarTareaButton);

				document.getElementById('editar-tarea-error').style.display = 'none';
				document.getElementById('editar-modulo-error').style.display = 'none';
				document.getElementById('editar-descripcion-error').style.display = 'none';

				const form = document.getElementById('editar-tarea-form');
				form.classList.remove('was-validated');
				const inputs = form.querySelectorAll('.is-invalid');
				inputs.forEach(input => input.classList.remove('is-invalid'));
			}
		});
	}

	// Initialize Sprint panel child task visibility (collapsed by default)
	const sprintToggleButtons = document.querySelectorAll('#sprint-table-body .btn-toggle-hijas');
	sprintToggleButtons.forEach(button => {
		const parentId = button.dataset.parentId;
		const childRows = document.querySelectorAll(`#sprint-table-body tr.tarea-hija[data-parent-id="${parentId}"]`);
		const icon = button.querySelector('i');

		button.classList.add('collapsed');
		icon.className = 'fa fa-chevron-right';

		childRows.forEach(row => {
			row.style.display = 'none';
		});
	});

});
</script>
<?php #endregion JS ?>

</body>
</html>
