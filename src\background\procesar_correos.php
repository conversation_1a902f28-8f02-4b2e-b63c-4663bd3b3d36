<?php
/**
 * Background Email Processor
 * Processes emails asynchronously to improve form response times
 */

// Prevent direct access
if (php_sapi_name() !== 'cli' && !isset($argv[1])) {
    http_response_code(403);
    exit('Access denied');
}

// Get the email data file from command line argument
$emailDataFile = $argv[1] ?? null;

if (!$emailDataFile || !file_exists($emailDataFile)) {
    error_log("Background email processor: Invalid or missing email data file");
    exit(1);
}

try {
    error_log("Background email processor started with file: {$emailDataFile}");

    // Load configuration and dependencies
    require_once dirname(__FILE__, 3) . '/config/config.php';
    require_once __ROOT__ . '/src/classes/phpmailercorreo.php';
    require_once __ROOT__ . '/vendor/autoload.php';

    error_log("Background processor: Dependencies loaded successfully");

    // Read email data
    $emailDataJson = file_get_contents($emailDataFile);
    if ($emailDataJson === false) {
        throw new Exception("Failed to read email data file: {$emailDataFile}");
    }

    $emailData = json_decode($emailDataJson, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("JSON decode error: " . json_last_error_msg());
    }

    if (!$emailData || !isset($emailData['tipo']) || !isset($emailData['params'])) {
        throw new Exception("Invalid email data format. Data: " . print_r($emailData, true));
    }

    $tipo = $emailData['tipo'];
    $emailParams = $emailData['params'];

    error_log("Background processor: Processing email type '{$tipo}' with params: " . json_encode($emailParams));

    // Connect to database for management emails
    global $conexion;

    if (!$conexion instanceof PDO) {
        error_log("Background processor: Database connection not available, attempting to reconnect...");
        // The connection should be established by config.php, but let's verify
    }
    
    // Process email based on type
    switch ($tipo) {
        case 'aliado_comercial':
            PHPMailerCorreo::enviar_correo_aliado_comercial($emailParams);
            error_log("Background: Correo de bienvenida enviado exitosamente a: " . ($emailParams['correo_aliado'] ?? 'N/A'));
            break;
            
        case 'gerencia_aliados':
            if (!$conexion instanceof PDO) {
                throw new Exception("Database connection required for management emails");
            }
            PHPMailerCorreo::enviar_correo_gerencia_aliados_comerciales($emailParams, $conexion);
            error_log("Background: Correo de notificación a gerencia enviado exitosamente para: " . ($emailParams['nombre_aliado'] ?? 'N/A'));
            break;
            
        default:
            throw new Exception("Unknown email type: {$tipo}");
    }
    
    // Clean up: remove the temporary file
    unlink($emailDataFile);
    
    error_log("Background email processing completed successfully for type: {$tipo}");
    
} catch (Exception $e) {
    error_log("Background email processing error: " . $e->getMessage());
    
    // Clean up the file even on error
    if (isset($emailDataFile) && file_exists($emailDataFile)) {
        unlink($emailDataFile);
    }
    
    exit(1);
}

exit(0);
?>
