<?php
/**
 * Test Form Submission with Detailed Logging
 * This will help us see exactly what happens during form processing
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering to capture any output
ob_start();

echo "=== Form Submission with Detailed Logging ===\n\n";

// Simulate the exact form submission
$_POST = [
    'is_ajax' => '1',
    'nombre_razon_social' => 'Test Logging Company',
    'cedula_nit' => '*********',
    'telefono_contacto' => '+57 ************',
    'correo_electronico' => '<EMAIL>', // Use the working email
    'ciudad_operacion' => 'Bogotá',
    'pais_operacion' => 'Colombia',
    'tipo_alianza' => 'Socio estrategico',
    'declaracion_veracidad' => '1',
    'aceptacion_terminos' => '1',
    'nombre_empresa_cliente' => 'Logging Client Company',
    'nombre_contacto_cliente' => 'Test Contact',
    'cargo_contacto_cliente' => 'Manager',
    'telefono_contacto_cliente' => '+57 ************',
    'correo_contacto_cliente' => '<EMAIL>',
    'ciudad_cliente' => 'Medellín',
    'pais_cliente' => 'Colombia',
    'descripcion_negocio' => 'Test business for logging and email verification',
    'servicios' => ['1', '2'] // We'll check if these exist
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "Starting form processing...\n";
echo "Background processing is currently: DISABLED (synchronous mode)\n\n";

// Custom error handler to capture any errors
set_error_handler(function($severity, $message, $file, $line) {
    echo "PHP Error: {$message} in {$file} on line {$line}\n";
});

try {
    // Include the form processor
    require_once dirname(__FILE__, 3) . '/config/config.php';
    
    // Check if services exist first
    echo "Checking if test services exist...\n";
    require_once __ROOT__ . '/vendor/autoload.php';
    
    use App\classes\Servicio;
    
    global $conexion;
    if ($conexion instanceof PDO) {
        $servicios = Servicio::get_list($conexion);
        if (!empty($servicios)) {
            $validIds = array_slice(array_map(function($s) { return $s->getId(); }, $servicios), 0, 2);
            $_POST['servicios'] = $validIds;
            echo "Using valid service IDs: [" . implode(', ', $validIds) . "]\n";
        } else {
            echo "WARNING: No services found in database\n";
        }
    }
    
    echo "\nProcessing form...\n";
    
    // Capture the start time
    $startTime = microtime(true);
    
    // Include the form processor
    include __ROOT__ . '/src/form_aliados_comerciales.php';
    
    $endTime = microtime(true);
    $processingTime = ($endTime - $startTime) * 1000; // Convert to milliseconds
    
    echo "\nForm processing completed in " . number_format($processingTime, 2) . " ms\n";
    
} catch (Exception $e) {
    echo "\nException caught: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "\nFatal error caught: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

// Get any output that was generated
$output = ob_get_contents();
ob_end_clean();

echo "\n=== Form Processor Output ===\n";
echo $output . "\n";

// Try to parse as JSON
if (!empty($output)) {
    $response = json_decode($output, true);
    if ($response) {
        echo "\n=== Parsed JSON Response ===\n";
        print_r($response);
        
        if (isset($response['success'])) {
            if ($response['success']) {
                echo "\n✓ Form submission was successful!\n";
                echo "Message: " . ($response['message'] ?? 'No message') . "\n";
                echo "\nEmails should have been sent synchronously during processing.\n";
                echo "Check your email inbox for the welcome email.\n";
            } else {
                echo "\n✗ Form submission failed!\n";
                echo "Error: " . ($response['message'] ?? 'Unknown error') . "\n";
            }
        }
    } else {
        echo "\n⚠ Output is not valid JSON\n";
    }
}

echo "\n=== Test Complete ===\n";
echo "Check the error logs for detailed email processing information.\n";

// Restore error handler
restore_error_handler();
?>
