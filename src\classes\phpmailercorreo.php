<?php

use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception as PHPMailerException;
use \App\classes\Configuracion;

// Include PHPMailer files
require_once __ROOT__ . '/vendor/autoload.php';

class PHPMailerCorreo
{
	/**
	 * @throws \Exception // Use fully qualified name for standard Exception
	 */
	private static function enviar_correo($paramref): void
	{
		$mail = new PHPMailer(true); // Initialize mail object outside try block for error info access
		try {
			$micorreo     = '<EMAIL>';
			$destinatario = $paramref['destinatario'];
			$subject      = $paramref['subject'];
			$body         = $paramref['body'];
			
			$mail = new PHPMailer(true);
			
			// SMTP configuration
			$mail->isSMTP();
			$mail->Host       = 'smtp.hostinger.com';    // SMTP server
			$mail->SMTPAuth   = true;
			$mail->Username   = $micorreo;
			$mail->Password   = 'Respcl.284#';
			$mail->SMTPSecure = PHPMailer::ENCRYPTION_SMTPS;
			$mail->Port       = 465;                     // TCP port
			
			// Configuración de codificación
			$mail->CharSet = 'UTF-8';
			
			// Email settings
			$mail->setFrom($micorreo, 'No responder ' . NOMBRE_EMPRESA_ABREV);
			$mail->addAddress($destinatario);
			$mail->Subject = $subject;
			$mail->isHTML(true);
			$mail->Body = $body;
			
			// Send email
			$mail->send();

		} catch (PHPMailerException $e) { // Catch specific PHPMailer exception
            // Rethrow as a generic \Exception, including PHPMailer's detailed error message
            throw new \Exception("Error al enviar correo: {$mail->ErrorInfo}");
        } catch (\Exception $e) { // Catch any other standard exceptions
            // Rethrow as a generic \Exception
            throw new \Exception("Error inesperado en el envío de correo: " . $e->getMessage());
        }
	}
	
	/**
	 * @throws \Exception // Use fully qualified name
	 */
	public static function enviar_correo_contactanos($paramref): void
	{
		try {
			//parametros
			$nombre   = $paramref['nombre'];
			$correo   = $paramref['correo'];
			$telefono = $paramref['telefono'];
			$asunto   = $paramref['asunto'];
			$mensaje  = $paramref['mensaje'];
			
			//validaciones
			validar_campovacio($nombre, 'Debe especificar el nombre');
			validar_campovacio($correo, 'Debe especificar el correo');
			validar_campovacio($telefono, 'Debe especificar el telefono');
			validar_campovacio($asunto, 'Debe especificar el asunto');
			validar_campovacio($mensaje, 'Debe especificar el mensaje');
			
			$body = 'Ha recibido un mensaje desde el formulario de contacto de la página web:<br><br>';
			$body .= '<b>Nombre:</b> ' . $nombre . '<br>';
			$body .= '<b>Correo:</b> ' . $correo . '<br>';
			$body .= '<b>Teléfono:</b> ' . $telefono . '<br>';
			$body .= '<b>Asunto:</b> ' . $asunto . '<br>';
			$body .= '<br>';
			$body .= '<b>Mensaje:</b><br> ' . $mensaje . '<br>';
			$body .= '<br>';
			$body .= '-- Esto es un mensaje automático.';
			
			//enviar correo
			$param                 = array();
			$param['subject']      = NOMBRE_EMPRESA_ABREV . ' - Mensaje desde formulario de contacto';
			//$param['destinatario'] = '<EMAIL>';
			$param['destinatario'] = '<EMAIL>';
			$param['body']         = $body;
			self::enviar_correo($param);

		} catch (\Exception $e) { // Catch the generic \Exception rethrown from enviar_correo or other issues
			// Simply rethrow the exception caught
			throw $e;
		}
	}

	/**
	 * Envía un correo de bienvenida a un nuevo aliado comercial.
	 *
	 * @param array $paramref Datos del aliado ['nombre_aliado', 'correo_aliado', 'tipo_alianza']
	 * @throws \Exception Si ocurre un error durante el envío. // Use fully qualified name
	 */
	public static function enviar_correo_aliado_comercial($paramref): void
	{
		// Define $correo_aliado early for use in catch block if needed
        $correo_aliado   = $paramref['correo_aliado'] ?? null;
		try {
			// Parameters from the form submission (using expected keys from the form)
			$nombre_aliado   = $paramref['nombre_aliado'] ?? null; // Should correspond to 'nombre_razon_social' input
			$correo_aliado   = $paramref['correo_aliado'] ?? null; // Should correspond to 'correo_electronico' input
			$tipo_alianza    = $paramref['tipo_alianza'] ?? 'Aliado'; // Should correspond to 'tipo_alianza' radio

			// Validations
			validar_campovacio($nombre_aliado, 'Debe especificar el nombre o razón social del aliado.');
			validar_campovacio($correo_aliado, 'Debe especificar el correo electrónico del aliado.');
			// Use filter_var for email validation as a standard approach
			if (!filter_var($correo_aliado, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('El formato del correo electrónico del aliado no es válido.');
            }

			// Construct email body
			$body = '<p>Estimado/a ' . htmlspecialchars($nombre_aliado) . ',</p>';
			$body .= '<p>Le damos una cordial bienvenida a la red de aliados comerciales de <strong>' . NOMBRE_EMPRESA_ABREV . '</strong>. Agradecemos sinceramente su interés y confianza al registrarse como <b>' . htmlspecialchars($tipo_alianza) . '</b>.</p>';
			$body .= '<p>Estamos muy entusiasmados con la posibilidad de colaborar juntos y explorar las oportunidades que esta alianza nos brindará. Creemos firmemente que trabajando en equipo podemos alcanzar grandes resultados.</p>';
            $body .= '<p>En breve, uno de nuestros representantes se pondrá en contacto con usted para conversar sobre los próximos pasos y cómo podemos empezar a trabajar en conjunto de la manera más efectiva.</p>';
			$body .= '<p>Si tiene alguna pregunta inmediata, no dude en responder a este correo o contactarnos a través de nuestros canales habituales.</p>';
			$body .= '<p>¡Gracias nuevamente por unirse a nosotros!</p>';
			$body .= '<br>';
			$body .= '<p>Atentamente,</p>';
			$body .= '<p>El equipo de ' . NOMBRE_EMPRESA_ABREV . '</p>'; // Use full name if available
            $body .= '<br>';
			$body .= '<p><small>-- Este es un mensaje automático. Por favor, no responda directamente a este correo si no es necesario. --</small></p>';

			// Prepare parameters for sending
			$param                 = array();
			$param['subject']      = '¡Bienvenido/a a la red de aliados de ' . NOMBRE_EMPRESA_ABREV . '!';
			$param['destinatario'] = $correo_aliado; // Send to the ally
			$param['body']         = $body;

			// Send email using the private method
			self::enviar_correo($param);

		} catch (\Exception $e) { // Catch the generic \Exception rethrown from enviar_correo or other issues
            // Log or handle the exception appropriately
			error_log("Error enviando correo a aliado comercial (" . ($correo_aliado ?? 'Correo no especificado') . "): " . $e->getMessage()); // Log the original error, handle null correo
			// Rethrow a user-friendly message, potentially wrapping the original exception if needed elsewhere
			throw new \Exception('Hubo un error al intentar enviar el correo de bienvenida al aliado comercial. Por favor, intente más tarde o contacte a soporte si el problema persiste.');
		}
	}

	/**
	 * Envía un correo de bienvenida a un nuevo freelancer.
	 *
	 * @param array $paramref Datos del freelancer ['nombre_freelancer', 'correo_freelancer']
	 * @throws \Exception Si ocurre un error durante el envío.
	 */
	public static function enviar_correo_freelancer($paramref): void
	{
		// Define variables early for potential use in catch block
		$correo_freelancer = $paramref['correo_freelancer'] ?? null;
		$nombre_freelancer = $paramref['nombre_freelancer'] ?? null;

		try {
			// Validations
			validar_campovacio($nombre_freelancer, 'Debe especificar el nombre completo del freelancer.');
			validar_campovacio($correo_freelancer, 'Debe especificar el correo electrónico del freelancer.');
			if (!filter_var($correo_freelancer, FILTER_VALIDATE_EMAIL)) {
				throw new Exception('El formato del correo electrónico del freelancer no es válido.');
			}

			// Construct email body
			$body = '<p>Estimado/a ' . htmlspecialchars($nombre_freelancer) . ',</p>';
			$body .= '<p>Le damos una calurosa bienvenida al equipo de freelancers de <strong>' . NOMBRE_EMPRESA_ABREV . '</strong>. Estamos muy agradecidos por su interés en colaborar con nosotros y por completar su registro.</p>';
			$body .= '<p>Valoramos enormemente el talento y la experiencia que profesionales como usted aportan a nuestra red. Su perfil ha sido recibido y será revisado por nuestro equipo.</p>';
			$body .= '<p>Pronto podríamos contactarle con oportunidades de proyectos que se ajusten a sus habilidades y experiencia.</p>';
			$body .= '<p>Si tiene alguna pregunta o necesita asistencia, no dude en ponerse en contacto con nosotros.</p>';
			$body .= '<p>¡Gracias nuevamente por unirse a nuestra comunidad de freelancers!</p>';
			$body .= '<br>';
			$body .= '<p>Atentamente,</p>';
			$body .= '<p>El equipo de ' . NOMBRE_EMPRESA_ABREV . '</p>';
			$body .= '<br>';
			$body .= '<p><small>-- Este es un mensaje automático. Por favor, no responda directamente a este correo si no es necesario. --</small></p>';

			// Prepare parameters for sending
			$param                 = array();
			$param['subject']      = '¡Bienvenido/a al equipo de Freelancers de ' . NOMBRE_EMPRESA_ABREV . '!';
			$param['destinatario'] = $correo_freelancer; // Send to the freelancer
			$param['body']         = $body;

			// Send email using the private method
			self::enviar_correo($param);

		} catch (\Exception $e) { // Catch exceptions from validation or enviar_correo
			// Log or handle the exception appropriately
			error_log("Error enviando correo de bienvenida a freelancer (" . ($correo_freelancer ?? 'Correo no especificado') . "): " . $e->getMessage());
			// Rethrow a user-friendly message
			throw new \Exception('Hubo un error al intentar enviar el correo de bienvenida al freelancer. Por favor, contacte a soporte si el problema persiste.');
		}
	}
	
	/**
	 * Envía un correo de notificación a la gerencia cuando un nuevo aliado comercial se registra.
	 *
	 * @param array $paramref Datos del aliado ['nombre_aliado', 'correo_aliado', 'tipo_alianza', ...]
	 * @param \PDO $conexion Conexión a la base de datos
	 * @throws \Exception Si ocurre un error durante el envío.
	 */
	public static function enviar_correo_gerencia_aliados_comerciales($paramref, \PDO $conexion): void
	{
		// Define variables early for potential use in catch block
		$nombre_aliado = $paramref['nombre_aliado'] ?? null;
		$correo_aliado = $paramref['correo_aliado'] ?? null;
		
		try {
			// Parameters from the form submission
			$nombre_aliado   = $paramref['nombre_aliado'] ?? null;
			$correo_aliado   = $paramref['correo_aliado'] ?? null;
			$tipo_alianza    = $paramref['tipo_alianza'] ?? 'Aliado';
			$telefono_aliado = $paramref['telefono_aliado'] ?? 'No especificado';
			
			// Validations
			validar_campovacio($nombre_aliado, 'Debe especificar el nombre o razón social del aliado.');
			validar_campovacio($correo_aliado, 'Debe especificar el correo electrónico del aliado.');
			
			// Get the list of management recipients from Configuracion
			$parametros = ['descripcion' => 'Destinatario gerencia formulario aliado comercial'];
			$configuraciones = Configuracion::get_list($parametros, $conexion);
			
			if (empty($configuraciones)) {
				error_log("No se encontraron destinatarios de gerencia para notificación de aliado comercial");
				return; // No recipients found, exit without sending
			}

			// Pre-validate email addresses to avoid processing invalid ones
			$destinatarios_validos = [];
			foreach ($configuraciones as $configuracion) {
				$destinatario = $configuracion->getValor();
				if (!empty($destinatario) && filter_var($destinatario, FILTER_VALIDATE_EMAIL)) {
					$destinatarios_validos[] = $destinatario;
				} else {
					error_log("Correo de destinatario de gerencia inválido o vacío: " . ($destinatario ?? 'No especificado'));
				}
			}

			if (empty($destinatarios_validos)) {
				error_log("No se encontraron destinatarios de gerencia válidos para notificación de aliado comercial");
				return; // No valid recipients found, exit without sending
			}

			// Construct email body
			$body = '<p><strong>Notificación de Nuevo Aliado Comercial</strong></p>';
			$body .= '<p>Se ha registrado un nuevo aliado comercial en el sistema.</p>';
			$body .= '<p><b>Detalles del aliado:</b></p>';
			$body .= '<ul>';
			$body .= '<li><b>Nombre/Razón Social:</b> ' . htmlspecialchars($nombre_aliado) . '</li>';
			$body .= '<li><b>Correo Electrónico:</b> ' . htmlspecialchars($correo_aliado) . '</li>';
			$body .= '<li><b>Teléfono:</b> ' . htmlspecialchars($telefono_aliado) . '</li>';
			$body .= '<li><b>Tipo de Alianza:</b> ' . htmlspecialchars($tipo_alianza) . '</li>';
			$body .= '</ul>';
			$body .= '<p>Por favor, revise la información y asigne un representante para dar seguimiento a este nuevo aliado comercial.</p>';
			$body .= '<br>';
			$body .= '<p>Atentamente,</p>';
			$body .= '<p>Sistema Automatizado de ' . NOMBRE_EMPRESA_ABREV . '</p>';
			$body .= '<br>';
			$body .= '<p><small>-- Este es un mensaje automático generado por el sistema. --</small></p>';

			// Prepare parameters for sending
			$param            = array();
			$param['subject'] = 'Nuevo Aliado Comercial Registrado - ' . htmlspecialchars($nombre_aliado);
			$param['body']    = $body;

			// Send email to each valid recipient
			foreach ($destinatarios_validos as $destinatario) {
				$param['destinatario'] = $destinatario;
				self::enviar_correo($param);
			}
			
		} catch (\Exception $e) {
			// Log or handle the exception appropriately
			error_log("Error enviando correo a gerencia sobre nuevo aliado comercial (" . ($nombre_aliado ?? 'Nombre no especificado') . "): " . $e->getMessage());
			// Rethrow a user-friendly message
			throw new \Exception('Hubo un error al intentar enviar la notificación a gerencia sobre el nuevo aliado comercial. Por favor, contacte a soporte si el problema persiste.');
		}
	}
	
	/**
	 * Envía un correo de notificación a la gerencia cuando un nuevo freelancer se registra.
	 *
	 * @param array $paramref Datos del freelancer ['nombre_freelancer', 'correo_freelancer', ...]
	 * @param \PDO $conexion Conexión a la base de datos
	 * @throws \Exception Si ocurre un error durante el envío.
	 */
	public static function enviar_correo_gerencia_freelancer($paramref, \PDO $conexion): void
	{
		// Define variables early for potential use in catch block
		$nombre_freelancer = $paramref['nombre_freelancer'] ?? null;
		$correo_freelancer = $paramref['correo_freelancer'] ?? null;
		
		try {
			// Parameters from the form submission
			$nombre_freelancer   = $paramref['nombre_freelancer'] ?? null;
			$correo_freelancer   = $paramref['correo_freelancer'] ?? null;
			$telefono_freelancer = $paramref['telefono_freelancer'] ?? 'No especificado';
			
			// Validations
			validar_campovacio($nombre_freelancer, 'Debe especificar el nombre completo del freelancer.');
			validar_campovacio($correo_freelancer, 'Debe especificar el correo electrónico del freelancer.');
			
			// Get the list of management recipients from Configuracion
			$parametros = ['descripcion' => 'Destinatario gerencia formulario freelancer'];
			$configuraciones = Configuracion::get_list($parametros, $conexion);
			
			if (empty($configuraciones)) {
				error_log("No se encontraron destinatarios de gerencia para notificación de freelancer");
				return; // No recipients found, exit without sending
			}
			
			// Construct email body
			$body = '<p><strong>Notificación de Nuevo Freelancer</strong></p>';
			$body .= '<p>Se ha registrado un nuevo freelancer en el sistema.</p>';
			$body .= '<p><b>Detalles del freelancer:</b></p>';
			$body .= '<ul>';
			$body .= '<li><b>Nombre Completo:</b> ' . htmlspecialchars($nombre_freelancer) . '</li>';
			$body .= '<li><b>Correo Electrónico:</b> ' . htmlspecialchars($correo_freelancer) . '</li>';
			$body .= '<li><b>Teléfono:</b> ' . htmlspecialchars($telefono_freelancer) . '</li>';
			$body .= '</ul>';
			$body .= '<p>Por favor, revise la información y asigne un representante para dar seguimiento a este nuevo freelancer.</p>';
			$body .= '<br>';
			$body .= '<p>Atentamente,</p>';
			$body .= '<p>Sistema Automatizado de ' . NOMBRE_EMPRESA_ABREV . '</p>';
			$body .= '<br>';
			$body .= '<p><small>-- Este es un mensaje automático generado por el sistema. --</small></p>';
			
			// Prepare parameters for sending
			$param = array();
			$param['subject'] = 'Nuevo Freelancer Registrado - ' . htmlspecialchars($nombre_freelancer);
			$param['body'] = $body;
			
			// Send email to each recipient in the list
			foreach ($configuraciones as $configuracion) {
				$destinatario = $configuracion->getValor();
				if (!empty($destinatario) && filter_var($destinatario, FILTER_VALIDATE_EMAIL)) {
					$param['destinatario'] = $destinatario;
					self::enviar_correo($param);
				} else {
					error_log("Correo de destinatario de gerencia inválido o vacío: " . ($destinatario ?? 'No especificado'));
				}
			}
			
		} catch (\Exception $e) {
			// Log or handle the exception appropriately
			error_log("Error enviando correo a gerencia sobre nuevo freelancer (" . ($nombre_freelancer ?? 'Nombre no especificado') . "): " . $e->getMessage());
			// Rethrow a user-friendly message
			throw new \Exception('Hubo un error al intentar enviar la notificación a gerencia sobre el nuevo freelancer. Por favor, contacte a soporte si el problema persiste.');
		}
	}
}
