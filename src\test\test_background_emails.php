<?php
/**
 * Test script for background email processing
 * This will help diagnose why emails aren't being sent
 */

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/classes/phpmailercorreo.php';
require_once __ROOT__ . '/vendor/autoload.php';

echo "=== Background Email System Diagnostic ===\n\n";

// Test 1: Check if background directory exists
echo "1. Checking background directory...\n";
$backgroundDir = __ROOT__ . '/src/background';
if (is_dir($backgroundDir)) {
    echo "✓ Background directory exists: {$backgroundDir}\n";
} else {
    echo "✗ Background directory missing: {$backgroundDir}\n";
    echo "Creating directory...\n";
    mkdir($backgroundDir, 0755, true);
}

// Test 2: Check if background processor exists
echo "\n2. Checking background processor file...\n";
$processorFile = $backgroundDir . '/procesar_correos.php';
if (file_exists($processorFile)) {
    echo "✓ Background processor exists: {$processorFile}\n";
} else {
    echo "✗ Background processor missing: {$processorFile}\n";
}

// Test 3: Check temp directory permissions
echo "\n3. Checking temp directory...\n";
$tempDir = sys_get_temp_dir();
echo "Temp directory: {$tempDir}\n";
if (is_writable($tempDir)) {
    echo "✓ Temp directory is writable\n";
} else {
    echo "✗ Temp directory is not writable\n";
}

// Test 4: Test creating temp file
echo "\n4. Testing temp file creation...\n";
try {
    $testFile = $tempDir . '/lunex_test_' . uniqid() . '.json';
    $testData = ['test' => 'data', 'timestamp' => time()];
    file_put_contents($testFile, json_encode($testData));
    
    if (file_exists($testFile)) {
        echo "✓ Temp file created successfully: {$testFile}\n";
        $content = file_get_contents($testFile);
        echo "✓ Temp file content readable\n";
        unlink($testFile);
        echo "✓ Temp file deleted successfully\n";
    } else {
        echo "✗ Failed to create temp file\n";
    }
} catch (Exception $e) {
    echo "✗ Error creating temp file: " . $e->getMessage() . "\n";
}

// Test 5: Test PHP CLI availability
echo "\n5. Testing PHP CLI...\n";
$phpPath = 'php';
$testCommand = "{$phpPath} --version";
$output = [];
$returnCode = 0;
exec($testCommand, $output, $returnCode);

if ($returnCode === 0) {
    echo "✓ PHP CLI available\n";
    echo "PHP Version: " . implode(' ', $output) . "\n";
} else {
    echo "✗ PHP CLI not available or not in PATH\n";
    echo "Return code: {$returnCode}\n";
}

// Test 6: Test background processor directly
echo "\n6. Testing background processor directly...\n";
if (file_exists($processorFile)) {
    // Create test email data
    $testEmailData = [
        'tipo' => 'aliado_comercial',
        'params' => [
            'nombre_aliado' => 'Test Aliado',
            'correo_aliado' => '<EMAIL>',
            'tipo_alianza' => 'Test Alliance',
            'telefono_aliado' => '123456789'
        ],
        'timestamp' => time()
    ];
    
    $testFile = $tempDir . '/lunex_test_email_' . uniqid() . '.json';
    file_put_contents($testFile, json_encode($testEmailData));
    
    echo "Created test email file: {$testFile}\n";
    
    // Test the processor
    $command = "php " . escapeshellarg($processorFile) . " " . escapeshellarg($testFile) . " 2>&1";
    echo "Running command: {$command}\n";
    
    $output = [];
    $returnCode = 0;
    exec($command, $output, $returnCode);
    
    echo "Return code: {$returnCode}\n";
    echo "Output:\n" . implode("\n", $output) . "\n";
    
    // Check if file was processed (should be deleted)
    if (file_exists($testFile)) {
        echo "⚠ Test file still exists (processor may have failed)\n";
        unlink($testFile); // Clean up
    } else {
        echo "✓ Test file was processed and deleted\n";
    }
} else {
    echo "✗ Cannot test processor - file doesn't exist\n";
}

// Test 7: Check error logs
echo "\n7. Checking recent error logs...\n";
$errorLogFile = ini_get('error_log');
if ($errorLogFile && file_exists($errorLogFile)) {
    echo "Error log file: {$errorLogFile}\n";
    $logContent = file_get_contents($errorLogFile);
    $lines = explode("\n", $logContent);
    $recentLines = array_slice($lines, -20); // Last 20 lines
    
    $emailRelatedLines = array_filter($recentLines, function($line) {
        return stripos($line, 'email') !== false || 
               stripos($line, 'correo') !== false || 
               stripos($line, 'background') !== false ||
               stripos($line, 'aliado') !== false;
    });
    
    if (!empty($emailRelatedLines)) {
        echo "Recent email-related log entries:\n";
        foreach ($emailRelatedLines as $line) {
            echo "  {$line}\n";
        }
    } else {
        echo "No recent email-related log entries found\n";
    }
} else {
    echo "Error log file not found or not configured\n";
}

// Test 8: Test the enviarCorreoEnBackground function
echo "\n8. Testing enviarCorreoEnBackground function...\n";

// Include the function from the main file
require_once __ROOT__ . '/src/form_aliados_comerciales.php';

try {
    $testParams = [
        'nombre_aliado' => 'Test Diagnostic',
        'correo_aliado' => '<EMAIL>',
        'tipo_alianza' => 'Test',
        'telefono_aliado' => '123456789'
    ];
    
    echo "Calling enviarCorreoEnBackground...\n";
    enviarCorreoEnBackground('aliado_comercial', $testParams);
    echo "✓ Function called without errors\n";
    
    // Wait a moment and check if any temp files were created
    sleep(1);
    $tempFiles = glob($tempDir . '/lunex_email_*.json');
    if (!empty($tempFiles)) {
        echo "⚠ Found unprocessed temp files:\n";
        foreach ($tempFiles as $file) {
            echo "  {$file}\n";
            // Clean up
            unlink($file);
        }
    } else {
        echo "✓ No unprocessed temp files found\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error calling enviarCorreoEnBackground: " . $e->getMessage() . "\n";
}

echo "\n=== Diagnostic Complete ===\n";
echo "\nIf emails are still not being sent, check:\n";
echo "1. SMTP configuration in PHPMailerCorreo class\n";
echo "2. Server firewall settings for outbound SMTP\n";
echo "3. Email provider authentication\n";
echo "4. Server exec() function permissions\n";
echo "5. Background process execution permissions\n";
?>
