<?php
/**
 * Test Form Submission Process
 * Simulates the actual form submission to test the complete flow
 */

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/vendor/autoload.php';

echo "=== Form Submission Test ===\n\n";

// Simulate form data
$_POST = [
    'is_ajax' => '1',
    'nombre_razon_social' => 'Test Company SA',
    'cedula_nit' => '*********',
    'telefono_contacto' => '+57 ************',
    'correo_electronico' => '<EMAIL>',
    'ciudad_operacion' => 'Bogotá',
    'pais_operacion' => 'Colombia',
    'tipo_alianza' => 'Socio estrategico',
    'declaracion_veracidad' => '1',
    'aceptacion_terminos' => '1',
    'nombre_empresa_cliente' => 'Client Company Ltd',
    'nombre_contacto_cliente' => '<PERSON>',
    'cargo_contacto_cliente' => 'CEO',
    'telefono_contacto_cliente' => '+57 ************',
    'correo_contacto_cliente' => '<EMAIL>',
    'ciudad_cliente' => 'Medellín',
    'pais_cliente' => 'Colombia',
    'descripcion_negocio' => 'Test business opportunity for diagnostic purposes',
    'servicios' => ['1', '2', '3'] // Assuming these service IDs exist
];

$_SERVER['REQUEST_METHOD'] = 'POST';

echo "Simulating form submission with test data...\n";
echo "POST data:\n";
print_r($_POST);

echo "\nCapturing output...\n";
ob_start();

try {
    // Include the form processor
    include __ROOT__ . '/src/form_aliados_comerciales.php';
    
    $output = ob_get_contents();
    ob_end_clean();
    
    echo "Form processor output:\n";
    echo $output . "\n";
    
    // Try to parse as JSON
    $response = json_decode($output, true);
    if ($response) {
        echo "\nParsed JSON response:\n";
        print_r($response);
        
        if (isset($response['success']) && $response['success']) {
            echo "\n✓ Form submission was successful!\n";
        } else {
            echo "\n✗ Form submission failed: " . ($response['message'] ?? 'Unknown error') . "\n";
        }
    } else {
        echo "\n⚠ Output is not valid JSON\n";
    }
    
} catch (Exception $e) {
    ob_end_clean();
    echo "\n✗ Exception during form processing: " . $e->getMessage() . "\n";
} catch (Error $e) {
    ob_end_clean();
    echo "\n✗ Fatal error during form processing: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
echo "Check the error logs for any email-related messages\n";
?>
