<?php

declare(strict_types=1);

namespace App\classes;

// Importar clases necesarias
use App\classes\Freelancer;
use Exception;
use PDO;
use PDOException;

class FreelancerExperiencia
{
	// --- Atributos ---
	private ?int              $id                         = null;
	private ?Freelancer       $freelancer                 = null;
	private ?string           $resumen_profesional        = null;
	private ?string           $certificaciones_relevantes = null;
	private ?string           $proyectos_relevantes       = null;
	private ?int              $sabe_ingles                = 0;
	private ?int              $sabe_frances               = 0;
	private ?int              $sabe_portugues             = 0;
	private ?string           $otros_idiomas              = null;
	
	/**
	 * Constructor: Inicializa las propiedades.
	 * Los objetos relacionados se inicializan con nuevas instancias.
	 */
	public function __construct()
	{
		$this->id                         = null;
		$this->freelancer                 = new Freelancer();
		$this->resumen_profesional        = null;
		$this->certificaciones_relevantes = null;
		$this->proyectos_relevantes       = null;
		$this->sabe_ingles                = 0;
		$this->sabe_frances               = 0;
		$this->sabe_portugues             = 0;
		$this->otros_idiomas              = null;
	}
	
	/**
	 * Método estático para construir desde un array (fila de DB).
	 * Crea objetos asociados (Freelancer, RangoExperiencia, NivelEducativo)
	 * y les asigna sus respectivos IDs.
	 *
	 * @param array $resultado Array asociativo.
	 *
	 * @return self Instancia de FreelancerExperiencia.
	 * @throws Exception Si hay error.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			// Llama al constructor que ya inicializa los objetos relacionados
			$objeto                             = new self();
			$objeto->id                         = isset($resultado['id']) ? (int)$resultado['id'] : null;
			$objeto->resumen_profesional        = $resultado['resumen_profesional'] ?? null;
			$objeto->certificaciones_relevantes = $resultado['certificaciones_relevantes'] ?? null;
			$objeto->proyectos_relevantes       = $resultado['proyectos_relevantes'] ?? null;
			$objeto->sabe_ingles                = isset($resultado['sabe_ingles']) ? (int)$resultado['sabe_ingles'] : 0;
			$objeto->sabe_frances               = isset($resultado['sabe_frances']) ? (int)$resultado['sabe_frances'] : 0;
			$objeto->sabe_portugues             = isset($resultado['sabe_portugues']) ? (int)$resultado['sabe_portugues'] : 0;
			$objeto->otros_idiomas              = $resultado['otros_idiomas'] ?? null;
			
			// Asignar ID al objeto Freelancer ya existente
			$freelancerId = isset($resultado['id_freelancer']) ? (int)$resultado['id_freelancer'] : null;
			if ($freelancerId !== null) {
				$objeto->freelancer->setId($freelancerId); // Asume setId() existe
			}

			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir FreelancerExperiencia: " . $e->getMessage());
		}
	}
	
	
	// --- Métodos de Acceso a Datos ---
	
	/**
	 * Obtiene un registro de experiencia por su ID y carga los objetos relacionados.
	 *
	 * @param int $id       ID de la experiencia.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto FreelancerExperiencia o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			$query     = <<<SQL
            SELECT * FROM freelancer_experiencia WHERE id = :id LIMIT 1
            SQL;
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			if ($resultado) {
				// Usa construct para inicializar con IDs
				$experiencia = self::construct($resultado);
				
				// Cargar los objetos relacionados completos si los IDs existen
				$id_freelancer = $experiencia->getFreelancer()?->getId();
				if ($id_freelancer) {
					$experiencia->setFreelancer(Freelancer::get($id_freelancer, $conexion));
				}

				return $experiencia;
			} else {
				return null;
			}
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener FreelancerExperiencia (ID: $id): " . $e->getMessage());
		} catch (Exception $e) {
			throw new Exception("Error al cargar datos relacionados para FreelancerExperiencia (ID: $id): " . $e->getMessage());
		}
	}
	
	/**
	 * Obtiene el registro de experiencia asociado a un Freelancer ID y carga los objetos relacionados.
	 *
	 * @param int $id_freelancer ID del freelancer.
	 * @param PDO $conexion      Conexión PDO.
	 *
	 * @return self|null Objeto FreelancerExperiencia o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getByFreelancerId(int $id_freelancer, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT fe.*
            FROM freelancer_experiencia fe
            WHERE
            	fe.id_freelancer = :id_freelancer
            LIMIT 1
            SQL;
			$statement = $conexion->prepare($query);
			$statement->bindValue(":id_freelancer", $id_freelancer, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);
			
			if ($resultado) {
				return self::construct($resultado);
			} else {
				return null;
			}
			
		} catch (PDOException $e) {
			throw new Exception("Error al obtener FreelancerExperiencia por ID Freelancer ($id_freelancer): " . $e->getMessage());
		} catch (Exception $e) {
			throw new Exception("Error al cargar datos relacionados para FreelancerExperiencia por ID Freelancer ($id_freelancer): " . $e->getMessage());
		}
	}
	
	/**
	 * Guarda (inserta o actualiza) la información de experiencia en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB o falta el ID del Freelancer.
	 */
	public function guardar(PDO $conexion): bool
	{
		if ($this->getFreelancer()?->getId() === null || $this->getFreelancer()->getId() <= 0) {
			throw new Exception("Se requiere un objeto Freelancer con ID válido asociado para guardar la experiencia.");
		}
		
		if ($this->getId() !== null && $this->getId() > 0) {
			return $this->_update($conexion);
		} else {
			return $this->_insert($conexion);
		}
	}
	
	/**
	 * Inserta un nuevo registro de experiencia en la base de datos. (Método privado)
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID del nuevo registro o false en caso de error.
	 * @throws Exception Si hay error en DB o falta el ID del Freelancer.
	 */
	private function _insert(PDO $conexion): int|bool
	{
		$idFreelancer = $this->getFreelancer()?->getId();
		if ($idFreelancer === null) {
			throw new Exception("Intento de insertar experiencia sin ID de Freelancer asociado.");
		}
		
		try {
			$query = <<<SQL
            INSERT INTO freelancer_experiencia (
                 id_freelancer
                ,resumen_profesional
                ,certificaciones_relevantes
                ,proyectos_relevantes
                ,sabe_ingles
                ,sabe_frances
                ,sabe_portugues
                ,otros_idiomas
            ) VALUES (
                 :id_freelancer
                ,:resumen_profesional
                ,:certificaciones_relevantes
                ,:proyectos_relevantes
                ,:sabe_ingles
                ,:sabe_frances
                ,:sabe_portugues
                ,:otros_idiomas
            )
            SQL;
			
			$statement = $conexion->prepare($query);
			
			$statement->bindValue(':id_freelancer', $idFreelancer, PDO::PARAM_INT);
			$statement->bindValue(':resumen_profesional', $this->getResumenProfesional(), PDO::PARAM_STR);
			$statement->bindValue(':certificaciones_relevantes', $this->getCertificacionesRelevantes(), PDO::PARAM_STR);
			$statement->bindValue(':proyectos_relevantes', $this->getProyectosRelevantes(), PDO::PARAM_STR);
			$statement->bindValue(':sabe_ingles', $this->getSabeIngles(), PDO::PARAM_INT);
			$statement->bindValue(':sabe_frances', $this->getSabeFrances(), PDO::PARAM_INT);
			$statement->bindValue(':sabe_portugues', $this->getSabePortugues(), PDO::PARAM_INT);
			$statement->bindValue(':otros_idiomas', $this->getOtrosIdiomas(), PDO::PARAM_STR);
			
			$success = $statement->execute();
			
			if ($success) {
				$this->setId((int)$conexion->lastInsertId());
				return true;
			} else {
				error_log("Error al ejecutar INSERT para FreelancerExperiencia: " . implode(" | ", $statement->errorInfo()));
				return false;
			}
		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al insertar FreelancerExperiencia: " . $e->getMessage());
		}
	}
	
	/**
	 * Actualiza un registro de experiencia existente en la base de datos. (Método privado)
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la actualización fue exitosa, False en caso contrario.
	 * @throws Exception Si el ID no está seteado, falta el ID del Freelancer, o hay error en DB.
	 */
	private function _update(PDO $conexion): bool
	{
		if ($this->getId() === null || $this->getId() <= 0) {
			throw new Exception("Se requiere un ID válido para actualizar la experiencia del freelancer.");
		}
		$idFreelancer = $this->getFreelancer()?->getId();
		if ($idFreelancer === null) {
			throw new Exception("Intento de actualizar experiencia sin ID de Freelancer asociado.");
		}
		
		try {
			$query = <<<SQL
            UPDATE freelancer_experiencia SET
                id_freelancer = :id_freelancer,
                resumen_profesional = :resumen_profesional,
                certificaciones_relevantes = :certificaciones_relevantes,
                proyectos_relevantes = :proyectos_relevantes,
                sabe_ingles = :sabe_ingles,
                sabe_frances = :sabe_frances,
                sabe_portugues = :sabe_portugues,
                otros_idiomas = :otros_idiomas
            WHERE id = :id
            SQL;
			
			$statement = $conexion->prepare($query);
			
			$statement->bindValue(':id_freelancer', $idFreelancer, PDO::PARAM_INT);
			$statement->bindValue(':resumen_profesional', $this->getResumenProfesional(), PDO::PARAM_STR);
			$statement->bindValue(':certificaciones_relevantes', $this->getCertificacionesRelevantes(), PDO::PARAM_STR);
			$statement->bindValue(':proyectos_relevantes', $this->getProyectosRelevantes(), PDO::PARAM_STR);
			$statement->bindValue(':sabe_ingles', $this->getSabeIngles(), PDO::PARAM_INT);
			$statement->bindValue(':sabe_frances', $this->getSabeFrances(), PDO::PARAM_INT);
			$statement->bindValue(':sabe_portugues', $this->getSabePortugues(), PDO::PARAM_INT);
			$statement->bindValue(':otros_idiomas', $this->getOtrosIdiomas(), PDO::PARAM_STR);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);
			
			return $statement->execute();
			
		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al actualizar FreelancerExperiencia (ID: {$this->getId()}): " . $e->getMessage());
		}
	}
	
	
	// --- Getters y Setters ---
	
	public function getId(): ?int
	{
		return $this->id;
	}
	
	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}
	
	public function getFreelancer(): ?Freelancer
	{
		return $this->freelancer;
	}
	
	public function setFreelancer(?Freelancer $freelancer): self
	{
		$this->freelancer = $freelancer;
		return $this;
	}
	
	public function getResumenProfesional(): ?string { return $this->resumen_profesional; }
	
	public function setResumenProfesional(?string $resumen_profesional): self
	{
		$this->resumen_profesional = $resumen_profesional;
		return $this;
	}
	
	public function getCertificacionesRelevantes(): ?string { return $this->certificaciones_relevantes; }
	
	public function setCertificacionesRelevantes(?string $certificaciones_relevantes): self
	{
		$this->certificaciones_relevantes = $certificaciones_relevantes;
		return $this;
	}
	
	public function getProyectosRelevantes(): ?string { return $this->proyectos_relevantes; }
	
	public function setProyectosRelevantes(?string $proyectos_relevantes): self
	{
		$this->proyectos_relevantes = $proyectos_relevantes;
		return $this;
	}
	

	
	public function getSabeIngles(): ?int { return $this->sabe_ingles; }
	
	public function setSabeIngles(?int $sabe_ingles): self
	{
		$this->sabe_ingles = ($sabe_ingles === 1) ? 1 : 0;
		return $this;
	}
	
	public function getSabeFrances(): ?int { return $this->sabe_frances; }
	
	public function setSabeFrances(?int $sabe_frances): self
	{
		$this->sabe_frances = ($sabe_frances === 1) ? 1 : 0;
		return $this;
	}
	
	public function getSabePortugues(): ?int { return $this->sabe_portugues; }
	
	public function setSabePortugues(?int $sabe_portugues): self
	{
		$this->sabe_portugues = ($sabe_portugues === 1) ? 1 : 0;
		return $this;
	}
	
	public function getOtrosIdiomas(): ?string { return $this->otros_idiomas; }
	
	public function setOtrosIdiomas(?string $otros_idiomas): self
	{
		$this->otros_idiomas = $otros_idiomas;
		return $this;
	}
	
} // Fin de la clase FreelancerExperiencia
