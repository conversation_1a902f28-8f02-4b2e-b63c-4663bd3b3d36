<?php
#region region DOCS

/** @var AliadoComercial[] $aliados_comerciales Array of AliadoComercial objects */

use App\classes\AliadoComercial;

// Import the class for type hinting and potential use

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode"> <?php // Assuming spanish 'es' and dark-mode preference ?>
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Aliados Comerciales</title> <?php // Update title ?>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Listado de aliados comerciales registrados" name="description"/> <?php // Update description ?>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php // Include the common head elements (CSS, etc.) ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Aliados Comerciales</h4> <?php // Update heading ?>
				<p class="mb-0 text-muted">Administra los aliados comerciales registrados en el sistema</p> <?php // Update paragraph ?>
			</div>
			<div class="ms-auto">
				<a href="laliados_comerciales_viables" class="btn btn-secondary">
					<i class="fa fa-arrow-left fa-fw me-1"></i> Ir a viables
				</a>
			</div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region PANEL ALIADOS COMERCIALES ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Aliados Comerciales Pendientes
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1" style="overflow: auto"> <?php // Responsive table container ?>
				<?php #region region TABLE ALIADOS COMERCIALES ?>
				<table class="table table-hover table-sm table-nowrap"> <?php // Table styling with nowrap ?>
					<thead>
					<tr>
						<th class="text-center">Acciones</th>
						<th>Fecha Registro</th>
						<th>Nombre/Razón Social</th>
						<th>Nombre Empresa</th>
						<th>Cédula/NIT</th>
						<th>Teléfono</th>
						<th>Correo</th>
						<th>Ciudad</th>
						<th>País</th>
						<th>Tipo Alianza</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="aliado-comercial-table-body"> 
					<?php if (!empty($aliados_comerciales)): ?>
						<?php foreach ($aliados_comerciales as $aliado_comercial): ?>
							<tr data-aliado-comercial-id="<?php echo $aliado_comercial->getId(); ?>"> 
								<td style="display: flex; flex-wrap: nowrap; gap: 0.25rem;"> <?php // Use Flexbox to keep buttons inline ?>
									<?php // View Profile Button (triggers POST) ?>
									<button type="button" class="btn btn-xs btn-info me-1 btn-view-profile"
									        title="Ver Perfil"
									        data-aliado-comercial-id="<?php echo $aliado_comercial->getId(); ?>">
										<i class="fa fa-eye"></i>
									</button>
									<!-- Deactivate Button (triggers SweetAlert) -->
									<button type="button" class="btn btn-xs btn-danger me-1 btn-deactivate-aliado-comercial"
									        title="Desactivar Aliado Comercial"
									        data-aliado-comercial-id="<?php echo $aliado_comercial->getId(); ?>"
									        data-aliado-comercial-nombre="<?php echo htmlspecialchars($aliado_comercial->getNombreRazonSocial() ?? 'este aliado comercial'); ?>">
										<i class="fa fa-trash"></i>
									</button>
									<!-- Create Project Button -->
									<button type="button" class="btn btn-xs btn-success btn-create-proyecto-aliado"
									        title="Crear Proyecto para este Aliado"
									        data-aliado-comercial-id="<?php echo $aliado_comercial->getId(); ?>">
										<i class="fa fa-briefcase"></i>
									</button>
									<!-- End Deactivate Button -->
								</td>
								<td>
									<?php
									$fecha_registro = $aliado_comercial->getFechaRegistro();
									if ($fecha_registro) {
										// Convert to DateTime and format as yyyy-MM-dd hh:mm AM/PM
										$fecha_obj = new DateTime($fecha_registro);
										echo htmlspecialchars($fecha_obj->format('Y-m-d h:i A'));
									} else {
										echo 'N/A';
									}
									?>
								</td>
								<td><?php echo htmlspecialchars($aliado_comercial->getNombreRazonSocial() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getClientePotencial()->getNombreEmpresa() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getCedulaNit() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getTelefonoContacto() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getCorreoElectronico() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getCiudadOperacion() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getPaisOperacion() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getTipoAlianza() ?? ''); ?></td>
								<?php // Add other cells if needed ?>
							</tr>
						<?php endforeach; ?>
					<?php else: ?>
						<?php // Message shown if no aliados comerciales are found ?>
						<tr>
							<td colspan="10" class="text-center">No hay aliados comerciales registrados para mostrar.</td> <?php // Updated colspan for new column ?>
						</tr>
					<?php endif; ?>
					</tbody>
				</table>
				<?php #endregion TABLE ALIADOS COMERCIALES ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL ALIADOS COMERCIALES ?>
		<?php #region region Hidden Form for Deactivation ?>
		<form id="deactivate-aliado-comercial-form" method="POST" action="laliados_comerciales" style="display: none;">
			<input type="hidden" name="action" value="deactivate">
			<input type="hidden" name="aliado_comercial_id" id="deactivate-aliado-comercial-id">
		</form>
		<?php #endregion Hidden Form ?>
		<?php #region region Hidden Form for Creating Project ?>
		<form id="create-proyecto-aliado-form" method="POST" action="iproyecto_aliado_comercial" style="display: none;">
			<?php // No action needed here, just the ID ?>
			<input type="hidden" name="aliado_comercial_id" id="create-proyecto-aliado-id">
		</form>
		<?php #endregion Hidden Form ?>

	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php // Include the core JS files (jQuery, Bootstrap, SweetAlert, etc.) ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js_adm.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        console.log("Aliado Comercial list page loaded.");
        const tableBody = document.getElementById('aliado-comercial-table-body');

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-deactivate-aliado-comercial');
                const viewProfileButton = event.target.closest('.btn-view-profile');
                const createProyectoButton = event.target.closest('.btn-create-proyecto-aliado'); // Get the new button

                // --- Handle Deactivate Click ---
				<?php #region region JS AJAX -- Handle Deactivate Click ?>
                if (deactivateButton) {
                    event.preventDefault(); // Prevent any default button action
                    const aliadoComercialId     = deactivateButton.dataset.aliadoComercialId;
                    const aliadoComercialNombre = deactivateButton.dataset.aliadoComercialNombre || 'este aliado comercial'; // Fallback name

                    swal({
                        title     : "Confirmar Desactivación",
                        text      : `¿Seguro que quieres desactivar a '${aliadoComercialNombre}'? No podrá iniciar sesión.`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                        .then((willDeactivate) => {
                            if (willDeactivate) {
                                // Set the ID in the hidden form and submit it
                                document.getElementById('deactivate-aliado-comercial-id').value = aliadoComercialId;
                                document.getElementById('deactivate-aliado-comercial-form').submit();
                            }
                        });
                }
				<?php #endregion JS AJAX -- Handle Deactivate Click ?>
                // --- Handle View Profile Click (POST Request) ---
                if (viewProfileButton) {
                    event.preventDefault(); // Prevent default button action
                    const aliadoComercialId = viewProfileButton.dataset.aliadoComercialId;

                    // Create a form dynamically
                    const form         = document.createElement('form');
                    form.method        = 'POST';
                    form.action        = 'caliado_comercial'; // Action points to your target PHP file
                    form.style.display = 'none'; // Hide the form

                    // Create a hidden input for the ID
                    const idInput = document.createElement('input');
                    idInput.type  = 'hidden';
                    idInput.name  = 'id'; // Name expected by caliado_comercial.php
                    idInput.value = aliadoComercialId;

                    // Append the input to the form
                    form.appendChild(idInput);

                    // Append the form to the body, submit it, and remove it
                    document.body.appendChild(form);
                    form.submit();
                    document.body.removeChild(form); // Clean up the DOM
                }

                // --- Handle Create Project Click (POST Request to new page) ---
                if (createProyectoButton) {
                    event.preventDefault(); // Prevent default button action
                    const aliadoComercialId = createProyectoButton.dataset.aliadoComercialId;

                    // Set the ID in the hidden form and submit it
                    document.getElementById('create-proyecto-aliado-id').value = aliadoComercialId;
                    document.getElementById('create-proyecto-aliado-form').submit();
                }

            });
        }
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>
