<?php
#region region DOCS

/** @var AliadoComercial[] $aliados_comerciales_viables Array of AliadoComercial objects */

use App\classes\AliadoComercial;

// Import the class for type hinting and potential use

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode"> <?php // Assuming spanish 'es' and dark-mode preference ?>
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Aliados Comerciales Viables</title> <?php // Update title ?>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Listado de aliados comerciales viables registrados" name="description"/> <?php // Update description ?>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php // Include the common head elements (CSS, etc.) ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Aliados Comerciales Viables</h4> <?php // Update heading ?>
				<p class="mb-0 text-muted">Consulta los aliados comerciales que han sido marcados como viables</p> <?php // Update paragraph ?>
			</div>
            <div class="ms-auto">
                <a href="laliados_comerciales" class="btn btn-secondary">
                    <i class="fa fa-arrow-left fa-fw me-1"></i> Ir a pendientes
                </a>
            </div>
		</div>

		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php #region region PANEL ALIADOS COMERCIALES VIABLES ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Aliados Comerciales Viables
				</h4>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1" style="overflow: auto"> <?php // Responsive table container ?>
				<?php #region region TABLE ALIADOS COMERCIALES VIABLES ?>
				<table class="table table-hover table-sm table-nowrap"> <?php // Table styling with nowrap ?>
					<thead>
					<tr>
						<th class="text-center">Acciones</th>
						<th>Fecha Registro</th>
						<th>Nombre/Razón Social</th>
						<th>Nombre Empresa</th>
						<th>Cédula/NIT</th>
						<th>Teléfono</th>
						<th>Correo</th>
						<th>Ciudad</th>
						<th>País</th>
						<th>Tipo Alianza</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="aliado-comercial-viable-table-body"> <?php // Changed ID slightly ?>
					<?php if (!empty($aliados_comerciales_viables)): ?>
						<?php foreach ($aliados_comerciales_viables as $aliado_comercial): ?>
							<tr data-aliado-comercial-id="<?php echo $aliado_comercial->getId(); ?>">
								<td style="display: flex; flex-wrap: nowrap; gap: 0.25rem;  justify-content: center;">
									<?php // View Profile Button (triggers POST) ?>
									<button type="button" class="btn btn-xs btn-info me-1 btn-view-profile"
									        title="Ver Perfil"
									        data-aliado-comercial-id="<?php echo $aliado_comercial->getId(); ?>">
										<i class="fa fa-eye"></i>
									</button>
									<?php // Deactivate and Create Project buttons removed ?>
								</td>
								<td>
									<?php
									$fecha_registro = $aliado_comercial->getFechaRegistro();
									if ($fecha_registro) {
										// Convert to DateTime and format as yyyy-MM-dd hh:mm AM/PM
										$fecha_obj = new DateTime($fecha_registro);
										echo htmlspecialchars($fecha_obj->format('Y-m-d h:i A'));
									} else {
										echo 'N/A';
									}
									?>
								</td>
								<td><?php echo htmlspecialchars($aliado_comercial->getNombreRazonSocial() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getClientePotencial()->getNombreEmpresa() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getCedulaNit() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getTelefonoContacto() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getCorreoElectronico() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getCiudadOperacion() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getPaisOperacion() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($aliado_comercial->getTipoAlianza() ?? ''); ?></td>
							</tr>
						<?php endforeach; ?>
					<?php else: ?>
						<?php // Message shown if no viable aliados comerciales are found ?>
						<tr>
							<td colspan="10" class="text-center">No hay aliados comerciales viables para mostrar.</td> <?php // Updated colspan for new column ?>
						</tr>
					<?php endif; ?>
					</tbody>
				</table>
				<?php #endregion TABLE ALIADOS COMERCIALES VIABLES ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL ALIADOS COMERCIALES VIABLES ?>
		<?php // Hidden forms removed ?>
	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php // Include the core JS files (jQuery, Bootstrap, SweetAlert, etc.) ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js_adm.view.php'; ?>

<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        console.log("Aliado Comercial Viable list page loaded.");
        const tableBody = document.getElementById('aliado-comercial-viable-table-body');

        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const viewProfileButton = event.target.closest('.btn-view-profile');

                // --- Handle View Profile Click (POST Request) ---
                if (viewProfileButton) {
                    event.preventDefault(); // Prevent default button action
                    const aliadoComercialId = viewProfileButton.dataset.aliadoComercialId;

                    // Create a form dynamically
                    const form         = document.createElement('form');
                    form.method        = 'POST';
                    form.action        = 'caliado_comercial'; // Action points to your target PHP file
                    form.style.display = 'none'; // Hide the form

                    // Create a hidden input for the ID
                    const idInput = document.createElement('input');
                    idInput.type  = 'hidden';
                    idInput.name  = 'id'; // Name expected by caliado_comercial.php
                    idInput.value = aliadoComercialId;

                    // Append the input to the form
                    form.appendChild(idInput);

                    // Append the form to the body, submit it, and remove it
                    document.body.appendChild(form);
                    form.submit();
                    document.body.removeChild(form); // Clean up the DOM
                }
                // --- Deactivate and Create Project handlers removed ---
            });
        }
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>