<?php
/**
 * Test AJAX Form Submission
 * Simulates the exact AJAX request that the form makes
 */

require_once dirname(__FILE__, 3) . '/config/config.php';

echo "=== AJAX Form Submission Test ===\n\n";

// Simulate the exact AJAX request
$postData = [
    'is_ajax' => '1',
    'nombre_razon_social' => 'Test Company AJAX',
    'cedula_nit' => '*********',
    'telefono_contacto' => '+57 ************',
    'correo_electronico' => '<EMAIL>', // Use the same email that worked
    'ciudad_operacion' => 'Bogotá',
    'pais_operacion' => 'Colombia',
    'tipo_alianza' => 'Socio estrategico',
    'declaracion_veracidad' => '1',
    'aceptacion_terminos' => '1',
    'nombre_empresa_cliente' => 'AJAX Client Company',
    'nombre_contacto_cliente' => '<PERSON>',
    'cargo_contacto_cliente' => 'CTO',
    'telefono_contacto_cliente' => '+57 ************',
    'correo_contacto_cliente' => '<EMAIL>',
    'ciudad_cliente' => 'Medellín',
    'pais_cliente' => 'Colombia',
    'descripcion_negocio' => 'AJAX test business opportunity for email diagnostic',
    'servicios' => ['1', '2'] // Assuming these service IDs exist
];

// Convert to URL-encoded format like a real form submission
$postString = http_build_query($postData);

echo "Simulating AJAX POST request to src/form_aliados_comerciales.php\n";
echo "POST data size: " . strlen($postString) . " bytes\n\n";

// Use cURL to simulate the exact request
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://localhost/lunex/src/form_aliados_comerciales.php');
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $postString);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/x-www-form-urlencoded',
    'X-Requested-With: XMLHttpRequest' // Simulate AJAX request
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

echo "Sending request...\n";
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "✗ cURL Error: {$error}\n";
    exit(1);
}

echo "HTTP Status Code: {$httpCode}\n\n";

// Split headers and body
$headerSize = strpos($response, "\r\n\r\n");
$headers = substr($response, 0, $headerSize);
$body = substr($response, $headerSize + 4);

echo "Response Headers:\n";
echo $headers . "\n\n";

echo "Response Body:\n";
echo $body . "\n\n";

// Try to parse JSON response
$jsonResponse = json_decode($body, true);
if ($jsonResponse) {
    echo "Parsed JSON Response:\n";
    print_r($jsonResponse);
    
    if (isset($jsonResponse['success'])) {
        if ($jsonResponse['success']) {
            echo "\n✓ AJAX submission was successful!\n";
            echo "Message: " . ($jsonResponse['message'] ?? 'No message') . "\n";
        } else {
            echo "\n✗ AJAX submission failed!\n";
            echo "Error: " . ($jsonResponse['message'] ?? 'Unknown error') . "\n";
        }
    }
} else {
    echo "⚠ Response is not valid JSON\n";
    echo "Raw response: " . substr($body, 0, 500) . (strlen($body) > 500 ? '...' : '') . "\n";
}

echo "\n=== Test Complete ===\n";
echo "Check the error logs for detailed information about email processing\n";
?>
