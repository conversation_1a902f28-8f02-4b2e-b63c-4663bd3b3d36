<?php
#region region DOCS

/** @var Freelancer[] $freelancers Array of Freelancer objects */

use App\classes\Freelancer;

// Import the class for type hinting and potential use

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode"> <?php // Assuming spanish 'es' and dark-mode preference ?>
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Freelancers</title> <?php // Update title ?>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Listado de freelancers registrados" name="description"/> <?php // Update description ?>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php // Include the common head elements (CSS, etc.) ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Freelancers</h4> <?php // Update heading ?>
				<p class="mb-0 text-muted">Administra los freelancers registrados en el sistema</p> <?php // Update paragraph ?>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region PANEL FREELANCERS ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">
					Listado de Freelancers Activos
				</h4>
				<?php // Optional: Add panel tools if needed ?>
			</div>
			<!-- BEGIN PANEL body -->
			<div class="p-1 table-nowrap" style="overflow: auto"> <?php // Responsive table container ?>
				<?php #region region TABLE FREELANCERS ?>
				<table class="table table-hover table-sm"> <?php // Table styling ?>
					<thead>
					<tr>
						<th>Acciones</th> <?php // Column for future buttons (edit, delete, view profile) ?>
						<th>Fecha Creación</th>
						<th>Nombre Completo</th>
						<th>Tipo Doc</th>
						<th>Documento</th>
						<th>Ciudad</th>
						<th>País</th>
						<th>Correo</th>
						<th>Teléfono</th>
					</tr>
					</thead>
					<tbody class="fs-12px" id="freelancer-table-body"> <?php // Unique ID for potential JS targeting ?>
					<?php if (!empty($freelancers)): ?>
						<?php foreach ($freelancers as $freelancer): ?>
							<tr data-freelancer-id="<?php echo $freelancer->getId(); ?>"> <?php // Add ID for potential JS ?>
								<td>
									<?php // View Profile Button (triggers POST) ?>
									<button type="button" class="btn btn-xs btn-info me-1 btn-view-profile"
									        title="Ver Perfil"
									        data-freelancer-id="<?php echo $freelancer->getId(); ?>">
										<i class="fa fa-eye"></i>
									</button>
									<!-- Deactivate Button (triggers SweetAlert) -->
									<button type="button" class="btn btn-xs btn-danger btn-deactivate-freelancer"
									        title="Desactivar Freelancer"
									        data-freelancer-id="<?php echo $freelancer->getId(); ?>"
									        data-freelancer-nombre="<?php echo htmlspecialchars($freelancer->getNombreCompleto() ?? 'este freelancer'); ?>">
										<i class="fa fa-trash"></i>
									</button>
									<!-- End Deactivate Button -->
								</td>
								<td><?php
									$fecha_creacion = $freelancer->getFechaCreacion();
									if ($fecha_creacion) {
										try {
											$fecha_obj = new DateTime($fecha_creacion);
											echo $fecha_obj->format('Y-m-d g:i A');
										} catch (Exception $e) {
											echo htmlspecialchars($fecha_creacion);
										}
									} else {
										echo 'N/A';
									}
								?></td>
								<td><?php echo htmlspecialchars($freelancer->getNombreCompleto() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($freelancer->getTipoDocumento() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($freelancer->getDocumentoIdentidad() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($freelancer->getCiudadResidencia() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($freelancer->getPaisResidencia() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($freelancer->getCorreoElectronico() ?? ''); ?></td>
								<td><?php echo htmlspecialchars($freelancer->getNumeroTelefono() ?? ''); ?></td>
								<?php // Add other cells if needed ?>
							</tr>
						<?php endforeach; ?>
					<?php else: ?>
						<?php // Message shown if no freelancers are found ?>
						<tr>
							<td colspan="9" class="text-center">No hay freelancers registrados para mostrar.</td> <?php // Colspan remains 9 as column count is the same ?>
						</tr>
					<?php endif; ?>
					</tbody>
				</table>
				<?php #endregion TABLE FREELANCERS ?>
			</div>
			<!-- END PANEL body -->
		</div>
		<?php #endregion PANEL FREELANCERS ?>
		<?php #region region Hidden Form for Deactivation ?>
		<form id="deactivate-freelancer-form" method="POST" action="lfreelancers" style="display: none;">
			<input type="hidden" name="action" value="deactivate">
			<input type="hidden" name="freelancer_id" id="deactivate-freelancer-id">
		</form>
		<?php #endregion Hidden Form ?>
	
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php // Include the core JS files (jQuery, Bootstrap, SweetAlert, etc.) ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js_adm.view.php'; ?>


<!-- ================== BEGIN PAGE LEVEL JS ================== -->
<script>
    document.addEventListener('DOMContentLoaded', function () {
        console.log("Freelancer list page loaded.");
        const tableBody = document.getElementById('freelancer-table-body');
        
        if (tableBody) {
            tableBody.addEventListener('click', function (event) {
                const deactivateButton = event.target.closest('.btn-deactivate-freelancer');
                const viewProfileButton = event.target.closest('.btn-view-profile');
                
                
                // --- Handle Deactivate Click ---
				<?php #region region JS AJAX -- Handle Deactivate Click ?>
                if (deactivateButton) {
                    event.preventDefault(); // Prevent any default button action
                    const freelancerId     = deactivateButton.dataset.freelancerId;
                    const freelancerNombre = deactivateButton.dataset.freelancerNombre || 'este freelancer'; // Fallback name
                    
                    swal({
                        title     : "Confirmar Desactivación",
                        text      : `¿Seguro que quieres desactivar a '${freelancerNombre}'? No podrá iniciar sesión.`,
                        icon      : "warning",
                        buttons   : {
                            cancel : {text: "Cancelar", value: null, visible: true, className: "", closeModal: true},
                            confirm: {text: "Confirmar", value: true, visible: true, className: "btn-danger", closeModal: true}
                        },
                        dangerMode: true,
                    })
                        .then((willDeactivate) => {
                            if (willDeactivate) {
                                // Set the ID in the hidden form and submit it
                                document.getElementById('deactivate-freelancer-id').value = freelancerId;
                                document.getElementById('deactivate-freelancer-form').submit();
                            }
                        });
                }
				<?php #endregion JS AJAX -- Handle Deactivate Click ?>
                // --- Handle View Profile Click (POST Request) ---
                if (viewProfileButton) {
                    event.preventDefault(); // Prevent default button action
                    const freelancerId = viewProfileButton.dataset.freelancerId;
                    
                    // Create a form dynamically
                    const form         = document.createElement('form');
                    form.method        = 'POST';
                    form.action        = 'cfreelancer'; // Action points to your target PHP file
                    form.style.display = 'none'; // Hide the form
                    
                    // Create a hidden input for the ID
                    const idInput = document.createElement('input');
                    idInput.type  = 'hidden';
                    idInput.name  = 'id'; // Name expected by cfreelancer.php
                    idInput.value = freelancerId;
                    
                    // Append the input to the form
                    form.appendChild(idInput);
                    
                    // Append the form to the body, submit it, and remove it
                    document.body.appendChild(form);
                    form.submit();
                    document.body.removeChild(form); // Clean up the DOM
                }
            });
        }
    });
</script>
<!-- ================== END PAGE LEVEL JS ================== -->

<?php #endregion JS ?>

</body>
</html>